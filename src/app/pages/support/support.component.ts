import { Component } from '@angular/core';
import { TranslationService } from '../../services/translation.service';
import { Title, Meta } from '@angular/platform-browser';
@Component({
  selector: 'app-support',
  imports: [],
  templateUrl: './support.component.html',
  styleUrl: './support.component.scss'
})
export class SupportComponent {
    constructor(
        private translationService: TranslationService,
        private titleService: Title,
        private metaService: Meta
    ) {}

    translate(key: string): string {
        return this.translationService.getTranslation(key);
    }

    ngOnInit(): void {
        this.titleService.setTitle('Hỗ trợ - CUB Vietnam');
        this.metaService.updateTag({ name: 'description', content: '<PERSON>ô tả trang hỗ trợ CUB Vietnam' });
    }
}
