<!-- FullPage.js Container -->
<div id="fullpage">
    <!-- Section 1: Hero Banner -->
    <div class="section" data-anchor="hero">
        <div class="header-container">
            <div class="container">
                <div class="reveal" data-anim="fade-up">
                <div class="main-banner-content-home">
                    <img src="assets/images/main-banner-home.png" alt="Cathay United Bank" class="main-banner-img pg-pc">
                    <img src="assets/images/home-banner-mobile.png" alt="Cathay United Bank" class="main-banner-img pg-mobile">
                    <a href="https://cubvn.onelink.me/13iz/zlldp" class="link-banner icon-app-ios" target="_blank"></a>
                    <a href="https://cubvn.onelink.me/13iz/zlldp" class="link-banner icon-app-android" target="_blank"></a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 2: Features -->
    <div class="section" data-anchor="features">
        <div class="features-section">
            <div class="container">
                <div class="features-section-content">
                    <div class="feature-card reveal" data-anim="fade-up">
                        <div class="icon icon-money">
                            <img src="assets/images/money-bag.png" alt="Money Icon">
                        </div>
                        <div class="feature-title" [innerHTML]="translate('feature_1')"></div>
                    </div>
                    <div class="feature-card reveal" data-anim="fade-up">
                        <div class="icon icon-document">
                            <img src="assets/images/paper.png" alt="Document Icon">
                        </div>
                        <div class="feature-title" [innerHTML]="translate('feature_2')"></div>
                    </div>

                    <div class="feature-card reveal" data-anim="fade-up">
                        <div class="icon icon-clock">
                            <img src="assets/images/memories.png" alt="Clock Icon">
                        </div>
                        <div class="feature-title" [innerHTML]="translate('feature_3')"></div>
                    </div>

                    <div class="feature-card reveal" data-anim="fade-up">
                        <div class="icon icon-user">
                            <img src="assets/images/id-card.png" alt="User Icon">
                        </div>
                        <div class="feature-title" [innerHTML]="translate('feature_4')"></div>
                    </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

            <div class="reveal" data-anim="fade-up">
    <!-- Section 3: Loan Calculator -->
    <div class="section fp-auto-height" data-anchor="calculator">
        <div class="loan-estimate-section">
            <div class="container">
                <div class="header-loan">
                    <div class="title" [innerHTML]="translate('loan_estimate_title')"></div>
                    <div class="subtitle" [innerHTML]="translate('loan_estimate_subtitle')"></div>
                </div>
                <div class="main-content">
                    <div class="left">
                        <div class="input-group">
                            <div class="input-group-content">
                                <div class="label" [innerHTML]="translate('loan_estimate_input_1')"></div>
                                <p class="input-value" [innerHTML]="formatCurrency(loanAmount)"></p>
                            </div>
                            <div class="slider-container">
                                <input type="range"
                                    [min]="minLoanAmount"
                                    [max]="maxLoanAmount"
                                    [step]="stepLoanAmount"
                                    [value]="loanAmount"
                                    (input)="onLoanAmountChange($event)"
                                    class="slider"
                                    [style.background]="getSliderBackground(loanAmount, minLoanAmount, maxLoanAmount)">
                            </div>
                            <div class="slider-labels">
                                <span>3.000.000</span>
                                <span>40.000.000</span>
                            </div>
                        </div>
                        <div class="input-group">
                            <div class="input-group-content">
                                <div class="label" [innerHTML]="translate('loan_estimate_input_2')"></div>
                                <span class="loan-term">
                                    {{ formatWithLeadingZeros(loanTerm) }} <span class="vnd-black">{{ translate('text_month') || 'tháng' }}</span>
                                </span>
                            </div>
                            <div class="slider-container">
                                <input type="range"
                                    [min]="minLoanTerm"
                                    [max]="maxLoanTerm"
                                    [step]="stepLoanTerm"
                                    [value]="loanTerm"
                                    (input)="onLoanTermChange($event)"
                                    class="slider"
                                    [style.background]="getSliderBackground(loanTerm, minLoanTerm, maxLoanTerm)">
                            </div>
                            <div class="slider-labels">
                                <span>3 <span class="vnd-black" [innerHTML]="translate('text_month')"></span></span>
                                <span>36 <span class="vnd-black" [innerHTML]="translate('text_month')"></span></span>
                            </div>
                        </div>
                    </div>
                    <div class="right">
                        <div class="right-content">
                            <div class="monthly-label" [innerHTML]="translate('loan_estimate_input_3')"></div>
                            <div class="monthly-value" [innerHTML]="formatCurrency(monthlyPayment)"></div>
                            <div class="monthly-desc">
                                <span class="vnd-black">
                                    ({{ translate('same_as') }} {{ dailyPayment !== null && dailyPayment !== undefined ? (dailyPayment | number) : '' }} VNĐ/{{ translate('text_day') || 'ngày' }})
                                </span>
                            </div>
            </div>
                        </div>
                        <div class="note" [innerHTML]="translate('loan_estimate_note')"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 4: Simple Disbursement Steps -->
    <div class="section fp-auto-height" data-anchor="steps">
        <div class="simple-disbursement-section">
            <div class="container">
                <div class="steps-container">
                    <h1 class="title-general" [innerHTML]="translate('simple_disbursement_title')"></h1>

                    <div class="steps-grid">
                        <!-- Step 1 -->
                        <div class="step">
                            <div class="step-number">01</div>
                            <div class="step-content" [innerHTML]="translate('simple_disbursement_step_1')"></div>
                        </div>

                        <!-- Step 2 -->
                        <div class="step">
                            <div class="step-number">02</div>
                            <div class="step-content" [innerHTML]="translate('simple_disbursement_step_2')"></div>
                        </div>

                        <!-- Step 3 -->
                        <div class="step">
                            <div class="step-number">03</div>
                            <div class="step-content" [innerHTML]="translate('simple_disbursement_step_3')"></div>
                        </div>

                        <!-- Step 4 -->
                        <div class="step">
                            <div class="step-number">04</div>
                            <div class="step-content" [innerHTML]="translate('simple_disbursement_step_4')"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 5: App Promotion -->
    <div class="section fp-auto-height" data-anchor="app">
        <div class="app-promo-section">
            <div class="container">
                <div class="app-promo-content">
                    <div class="app-promo-left">
                        <div class="app-promo-left-content">
                            <img src="assets/images/icon-cathay.png" alt="Cathay App Logo" class="app-logo">
                            <div class="app-slogan">
                                <span class="slogan-main" [innerHTML]="translate('app_slogan')"></span>
                            </div>
                        </div>
                        <hr class="app-divider">
                        <div class="app-desc" [innerHTML]="translate('app_download_title')"></div>
                        <div class="app-store-btns">
                            <a href="https://cubvn.onelink.me/13iz/zlldp" class="store-btn" target="_blank">
                                <img src="assets/images/app-store-adroid.png" alt="Google Play" />
                            </a>
                            <a href="https://cubvn.onelink.me/13iz/zlldp" class="store-btn iso-icon" target="_blank">
                                <img src="assets/images/app-store-ios.png" alt="App Store" />
                            </a>
                        </div>
                    </div>
                    <div class="app-promo-right">
                        <img src="assets/images/pic-ad-down-app.png" alt="CUB Girl" class="app-girl-img">
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Section 6: FAQ -->
    <div class="section fp-auto-height" data-anchor="faq">
        <div class="faq-section">
            <div class="container">
                <div class="header">
                    <h1 [innerHTML]="translate('faq_title')"></h1>
                    <p [innerHTML]="translate('faq_desc')"></p>
                    <p [innerHTML]="translate('faq_desc_2')"></p>
                </div>

                <div class="nav-buttons">
                    <button
                        class="nav-button"
                        [ngClass]="{ 'active-tab1': activeTab === 1, 'inactive': activeTab !== 1 }"
                        id="tab1"
                        [innerHTML]="translate('faq_tab_1')"
                        (click)="setActiveTab(1)"
                    ></button>
                    <button
                        class="nav-button"
                        [ngClass]="{ 'active-tab2': activeTab === 2, 'inactive': activeTab !== 2 }"
                        id="tab2"
                        [innerHTML]="translate('faq_tab_2')"
                        (click)="setActiveTab(2)"
                    ></button>
                    <button
                        class="nav-button"
                        [ngClass]="{ 'active-tab3': activeTab === 3, 'inactive': activeTab !== 3 }"
                        id="tab3"
                        [innerHTML]="translate('faq_tab_3')"
                        (click)="setActiveTab(3)"
                    ></button>
                    <button
                        class="nav-button"
                        [ngClass]="{ 'active-tab4': activeTab === 4, 'inactive': activeTab !== 4 }"
                        id="tab4"
                        [innerHTML]="translate('faq_tab_4')"
                        (click)="setActiveTab(4)"
                    ></button>
                </div>

                <div class="faq-container">
                    <div class="faq-image">
                        <img src="assets/images/pic-talent.png" alt="Person thinking with a phone">
                        <div class="floating-elements">
                        </div>
                    </div>
                    <div class="faq-content">
                        <!-- Tab 1 Content -->
                        <div id="tab1-content" class="tab-content" [class.active]="activeTab === 1" *ngIf="activeTab === 1">
                            <div class="faq-item" *ngFor="let q of [1,2,3,4,5]; let i = index">
                                <div class="faq-header" (click)="toggleFaq(i)">
                                    <div class="faq-question" [innerHTML]="translate('faq_tab1_question_' + (i+1))"></div>
                                    <div class="dropdown-icon" [class.rotated]="openFaqIndex === i" style="position: absolute;"></div>
                                </div>
                                <div class="faq-answer" [style.display]="openFaqIndex === i ? 'block' : 'none'" [innerHTML]="translate('faq_tab1_answer_' + (i+1))"></div>
                            </div>
                        </div>

                        <!-- Tab 2 Content -->
                        <div id="tab2-content" class="tab-content" [class.active]="activeTab === 2" *ngIf="activeTab === 2">
                            <div class="faq-item" *ngFor="let q of [1,2,3,4,5]; let i = index">
                                <div class="faq-header" (click)="toggleFaq(i)">
                                    <div class="faq-question" [innerHTML]="translate('faq_tab2_question_' + (i+1))"></div>
                                    <div class="dropdown-icon purple" [class.rotated]="openFaqIndex === i" style="position: absolute;"></div>
                                </div>
                                <div class="faq-answer" [style.display]="openFaqIndex === i ? 'block' : 'none'" [innerHTML]="translate('faq_tab2_answer_' + (i+1))"></div>
                            </div>
                        </div>

                        <!-- Tab 3 Content -->
                        <div id="tab3-content" class="tab-content" [class.active]="activeTab === 3" *ngIf="activeTab === 3">
                            <div class="faq-item" *ngFor="let q of [1,2,3,4]; let i = index">
                                <div class="faq-header" (click)="toggleFaq(i)">
                                    <div class="faq-question" [innerHTML]="translate('faq_tab3_question_' + (i+1))"></div>
                                    <div class="dropdown-icon" [class.rotated]="openFaqIndex === i" style="position: absolute;"></div>
                                </div>
                                <div class="faq-answer" [style.display]="openFaqIndex === i ? 'block' : 'none'" [innerHTML]="translate('faq_tab3_answer_' + (i+1))"></div>
                            </div>
                        </div>

                        <!-- Tab 4 Content -->
                        <div id="tab4-content" class="tab-content" [class.active]="activeTab === 4" *ngIf="activeTab === 4">
                            <div class="faq-item">
                                <div class="faq-header" (click)="toggleFaq(0)">
                                    <div class="faq-question" [innerHTML]="translate('faq_tab4_question_1')"></div>
                                    <div class="dropdown-icon" [class.rotated]="openFaqIndex === 0" style="position: absolute;"></div>
                                </div>
                                <div class="faq-answer" [style.display]="openFaqIndex === 0 ? 'block' : 'none'" [innerHTML]="translate('faq_tab4_answer_1')"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Scroll indicators (will be automatically generated by fullPage.js) -->
<!-- Custom scroll down indicator for first section -->
<div class="scroll-indicator" *ngIf="!isMobile()">
    <div class="scroll-arrow" (click)="scrollToNext()">
        <span></span>
        <span></span>
        <span></span>
    </div>
    <div class="scroll-text">{{ translate('scroll_down') || 'Cuộn xuống' }}</div>
</div>