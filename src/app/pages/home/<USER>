/* FullPage.js Custom Styles */
#fullpage {
  .section {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;

    // Auto-height sections should not be forced to 100vh
    &.fp-auto-height {
      min-height: auto;
      height: auto !important;
      align-items: flex-start;
    }

    // Ensure sections take full height when needed
    &.fp-section {
      height: 100vh !important;
    }
  }
}

/* Custom Navigation Dots */
#fp-nav {
  ul {
    li {
      a {
        span {
          background: rgba(255, 255, 255, 0.5);
          border: 2px solid #3985c0;

          &:hover {
            background: #3985c0;
          }
        }

        &.active span {
          background: #3985c0;
        }
      }
    }
  }
}

/* Section specific styles */
.section {
  &[data-anchor="hero"] {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);

    .header-container {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  &[data-anchor="features"] {
    background: #f8f9fa;

    .features-section {
      width: 100%;
      padding: 50px 0;
    }
  }

  &[data-anchor="calculator"] {
    background: #ffffff;

    .loan-estimate-section {
      width: 100%;
      margin-top: 0;
      padding: 50px 0;
    }
  }

  &[data-anchor="steps"] {
    background: #f1f3f4;

    .simple-disbursement-section {
      width: 100%;
      margin-top: 0;
      padding: 50px 0;
    }
  }

  &[data-anchor="app"] {
    background: #ededed;

    .app-promo-section {
      width: 100%;
      margin-top: 0;
      padding: 50px 0;
      background: transparent;
    }
  }

  &[data-anchor="faq"] {
    background: #ffffff;

    .faq-section {
      width: 100%;
      padding: 50px 0;
    }
  }
}

/* Responsive adjustments for fullPage.js */
@media (max-width: 768px) {
  #fullpage {
    .section {
      padding: 20px 0;

      &.fp-section {
        height: auto !important;
        min-height: 100vh;
      }
    }
  }

  /* Disable fullPage.js on mobile for better UX */
  .fp-enabled body {
    overflow: auto !important;
  }
}

/* Ensure content doesn't overflow */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 15px;
}

/* Animation classes for content */
.fp-viewing-hero .header-container {
  animation: fadeInUp 1s ease-out;
}

.fp-viewing-features .features-section-content {
  animation: fadeInUp 1s ease-out 0.3s both;
}

.fp-viewing-calculator .loan-estimate-section {
  animation: fadeInUp 1s ease-out 0.3s both;
}

.fp-viewing-steps .simple-disbursement-section {
  animation: fadeInUp 1s ease-out 0.3s both;
}

.fp-viewing-app .app-promo-section {
  animation: fadeInUp 1s ease-out 0.3s both;
}

.fp-viewing-faq .faq-section {
  animation: fadeInUp 1s ease-out 0.3s both;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Fix for existing styles to work with fullPage.js */
.header-container,
.features-section,
.loan-estimate-section,
.simple-disbursement-section,
.app-promo-section,
.faq-section {
  position: relative;
  z-index: 1;
}

/* Custom scroll indicator */
.scroll-indicator {
  position: fixed;
  bottom: 30px;
  left: 50%;
  transform: translateX(-50%);
  z-index: 1000;
  text-align: center;
  color: white;

  .scroll-arrow {
    cursor: pointer;
    margin-bottom: 10px;

    span {
      display: block;
      width: 20px;
      height: 20px;
      border-bottom: 2px solid white;
      border-right: 2px solid white;
      transform: rotate(45deg);
      margin: -10px auto;
      animation: scrollDown 2s infinite;

      &:nth-child(2) {
        animation-delay: 0.2s;
      }

      &:nth-child(3) {
        animation-delay: 0.4s;
      }
    }

    &:hover span {
      border-color: #3985c0;
    }
  }

  .scroll-text {
    font-size: 14px;
    font-weight: 500;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
  }
}

@keyframes scrollDown {
  0% {
    opacity: 0;
    transform: rotate(45deg) translate(-20px, -20px);
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: rotate(45deg) translate(20px, 20px);
  }
}
/* Features section animations: staggered cards */
.features-section-content {
  opacity: 0;
  transform: translateY(20px);
  animation: featuresFadeIn 0.6s ease-out 0.1s forwards;

  .feature-card {
    opacity: 0;
    transform: translateY(20px);
    animation: featureCardIn 0.5s ease-out forwards;
  }

  .feature-card:nth-child(1) { animation-delay: 0.15s; }
  .feature-card:nth-child(2) { animation-delay: 0.3s; }
  .feature-card:nth-child(3) { animation-delay: 0.45s; }
  .feature-card:nth-child(4) { animation-delay: 0.6s; }
}

@keyframes featuresFadeIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes featureCardIn {
  from { opacity: 0; transform: translateY(20px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Hide scroll indicator on mobile */
@media (max-width: 768px) {
  .scroll-indicator {
    display: none;
  }
}
/* Generic reveal animations for section elements */
.reveal {
  opacity: 0;
  transform: translateY(20px);
  transition: opacity 600ms ease, transform 600ms ease;

  &.show {
    opacity: 1;
    transform: none;
  }

  &[data-anim="fade-up"] {
    transform: translateY(24px);
  }

  &[data-anim="fade-left"] {
    transform: translateX(24px);
  }

  &[data-anim="fade-right"] {
    transform: translateX(-24px);
  }

  &[data-anim="zoom-in"] {
    transform: scale(0.96);
  }
}
