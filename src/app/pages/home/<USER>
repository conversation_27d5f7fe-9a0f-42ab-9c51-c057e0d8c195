import { Component, OnInit, On<PERSON><PERSON>roy, AfterViewInit, PLATFORM_ID, Inject } from '@angular/core';
import { CommonModule, isPlatformBrowser } from '@angular/common';
import { TranslationService } from '../../services/translation.service';
import { Title, Meta } from '@angular/platform-browser';

declare global {
  interface Window {
    fullpage: any;
  }
}

@Component({
    selector: 'app-home',
    standalone: true,
    imports: [CommonModule],
    templateUrl: './home.component.html',
    styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit, AfterViewInit, OnDestroy {
    activeTab: number = 1;
    openFaqIndex: number | null = null;
    private fullPageInstance: any;

    // Thêm các biến cho loan calculator
    loanAmount = 40000000;
    loanTerm = 3;
    minLoanAmount = 3000000;
    maxLoanAmount = 40000000;
    minLoanTerm = 3;
    maxLoanTerm = 36;
    stepLoanAmount = 1000000;
    stepLoanTerm = 3;

    monthlyPayment = 0;
    dailyPayment: number = 0;

    constructor(
        private translationService: TranslationService,
        private titleService: Title,
        private metaService: Meta,
        @Inject(PLATFORM_ID) private platformId: Object) {
        this.updatePayments();
    }

    ngOnInit(): void {
        this.titleService.setTitle('Trang chủ - CUB Vietnam');
        this.metaService.updateTag({ name: 'description', content: 'Mô tả trang chủ CUB Vietnam' });
    }

    ngAfterViewInit(): void {
        if (isPlatformBrowser(this.platformId)) {
            // Wait for DOM and scripts to load
            setTimeout(() => {
                this.initializeFullPage();
            }, 1000);
        }
    }

    ngOnDestroy(): void {
        if (this.fullPageInstance && isPlatformBrowser(this.platformId)) {
            this.fullPageInstance.destroy('all');
        }
    }

    private initializeFullPage(): void {
        try {
            // Check if fullpage is available in global scope
            if (typeof (window as any).fullpage === 'undefined') {
                console.error('fullPage.js is not loaded. Retrying in 1 second...');
                // Retry after a longer delay
                setTimeout(() => {
                    this.initializeFullPage();
                }, 1000);
                return;
            }

            console.log('Initializing fullPage.js...');

            // Use the global fullpage constructor from window
            const fullPageConstructor = (window as any).fullpage;

            this.fullPageInstance = new fullPageConstructor('#fullpage', {
                // Basic options
                licenseKey: 'OPEN-SOURCE-GPLV3-LICENSE', // GPLv3 projects only; use your commercial key for commercial use
                anchors: ['hero', 'features', 'calculator', 'steps', 'app', 'faq'],

                // Navigation
                navigation: true,
                navigationPosition: 'right',
                navigationTooltips: ['Trang chủ', 'Tính năng', 'Tính toán', 'Quy trình', 'Ứng dụng', 'FAQ'],
                showActiveTooltip: true,

                // Remove "Made with fullPage.js" watermark
                credits: { enabled: false },

                // Scrolling
                css3: true,
                scrollingSpeed: 700,
                autoScrolling: true,
                fitToSection: false,
                fitToSectionDelay: 600,
                scrollBar: true,
                easing: 'easeInOutCubic',
                easingcss3: 'ease',
                loopBottom: false,
                loopTop: false,
                touchSensitivity: 15,

                // Accessibility
                keyboardScrolling: true,
                animateAnchor: true,
                recordHistory: true,

                // Design
                verticalCentered: false, // Set to false for better content control
                paddingTop: '0',
                paddingBottom: '0',

                // Responsive settings
                responsiveWidth: 768, // Disable fullPage.js on screens smaller than 768px
                responsiveHeight: 0,

                // Custom selectors
                sectionSelector: '.section',
                slideSelector: '.slide',

                // Scroll overflow: disable internal scrollbars, use page scroll instead
                scrollOverflow: false,

                // Normal scroll elements (for sliders, forms, etc.)
                normalScrollElements: '.slider, .faq-content, input, textarea, select',

                // Events
                onLeave: (origin: any, destination: any) => {
                    console.log('Leaving section ' + origin.index + ' to section ' + destination.index);
                    // Add custom animations or logic here
                },
                afterLoad: (_origin: any, destination: any) => {
                    console.log('After loading section ' + destination.index);
                    // Add section-specific logic here
                    this.onSectionLoad(destination.anchor);
                },
                afterRender: () => {
                    console.log('FullPage.js has been rendered');
                },
                afterResize: (width: number, height: number) => {
                    console.log('FullPage.js has been resized to: ' + width + 'x' + height);
                },
                afterResponsive: (isResponsive: boolean) => {
                    console.log('FullPage.js responsive mode: ' + isResponsive);
                    if (isResponsive) {
                        // Handle responsive mode
                        document.body.style.overflow = 'auto';
                    }
                }
            });

            console.log('fullPage.js initialized successfully');

        } catch (error) {
            console.error('Error initializing fullPage.js:', error);
        }
    }

    private onSectionLoad(anchor: string): void {
        // Trigger reveal animations for elements inside the active section
        this.revealInSection(anchor);

        // Optional: section-specific logic
        switch (anchor) {
            case 'hero':
                break;
            case 'features':
                break;
            case 'calculator':
                break;
            case 'steps':
                break;
            case 'app':
                break;
            case 'faq':
                this.openFaqIndex = null;
                break;
            default:
                break;
        }
    }

    // Add 'show' class to .reveal items inside a section (with stagger)
    private revealInSection(anchor: string): void {
        if (!isPlatformBrowser(this.platformId)) return;
        const sectionEl = document.querySelector(`.section[data-anchor="${anchor}"]`);
        if (!sectionEl) return;
        const elements = sectionEl.querySelectorAll<HTMLElement>('.reveal');
        // Reset state so animation can replay
        elements.forEach((el) => el.classList.remove('show'));
        // Staggered reveal
        elements.forEach((el, idx) => {
            setTimeout(() => el.classList.add('show'), idx * 120);
        });
    }

    translate(key: string): string {
        return this.translationService.getTranslation(key);
    }

    setActiveTab(tab: number) {
        this.activeTab = tab;
        this.openFaqIndex = null; // Đóng tất cả FAQ khi đổi tab
    }

    toggleFaq(index: number) {
        this.openFaqIndex = this.openFaqIndex === index ? null : index;
    }

    // ===== Loan Calculator Logic =====
    PMT(rate: number, nper: number, pv: number): number {
        return (rate * pv) / (1 - Math.pow(1 + rate, -nper));
    }

    calculateMonthlyPayment(principal: number, termMonths: number): number {
        const monthlyRate = 0.40 / 12;
        return Math.round(this.PMT(monthlyRate, termMonths, principal));
    }

    formatCurrency(amount: number): string {
        return new Intl.NumberFormat("vi-VN").format(amount) + ' <span class="vnd-black">VNĐ</span>';
    }

    formatWithLeadingZeros(num: number): string {
        return num < 10 ? "0" + num : num.toString();
    }

    onLoanAmountChange(event: any) {
        this.loanAmount = +event.target.value;
        this.updatePayments();
    }

    onLoanTermChange(event: any) {
        this.loanTerm = +event.target.value;
        this.updatePayments();
    }

    updatePayments() {
        this.monthlyPayment = this.calculateMonthlyPayment(this.loanAmount, this.loanTerm);
        this.dailyPayment = Math.round(this.monthlyPayment / 30);
    }

    getSliderBackground(value: number, min: number, max: number): string {
        const percentage = ((value - min) / (max - min)) * 100;
        return `linear-gradient(to right, #3985c0 0%, #3985c0 ${percentage}%, #b9e1ff ${percentage}%, #b9e1ff 100%)`;
    }

    // FullPage.js navigation methods
    goToSection(sectionIndex: number): void {
        if (this.fullPageInstance && isPlatformBrowser(this.platformId)) {
            this.fullPageInstance.moveTo(sectionIndex);
        }
    }

    goToSectionByAnchor(anchor: string): void {
        if (this.fullPageInstance && isPlatformBrowser(this.platformId)) {
            this.fullPageInstance.moveTo(anchor);
        }
    }

    // Method to scroll to next section
    scrollToNext(): void {
        if (this.fullPageInstance && isPlatformBrowser(this.platformId)) {
            this.fullPageInstance.moveSectionDown();
        }
    }

    // Method to scroll to previous section
    scrollToPrevious(): void {
        if (this.fullPageInstance && isPlatformBrowser(this.platformId)) {
            this.fullPageInstance.moveSectionUp();
        }
    }

    // Check if device is mobile
    isMobile(): boolean {
        if (isPlatformBrowser(this.platformId)) {
            return window.innerWidth <= 768;
        }
        return false;
    }
}
