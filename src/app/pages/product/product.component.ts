import { Component } from '@angular/core';
import { TranslationService } from '../../services/translation.service';
import { Meta, Title } from '@angular/platform-browser';

@Component({
    selector: 'app-product',
    standalone: true,
    templateUrl: './product.component.html',
    styleUrl: './product.component.scss'
})
export class ProductComponent {
    constructor(
        private translationService: TranslationService,
        private titleService: Title,
        private metaService: Meta
    ) {}

    translate(key: string): string {
        return this.translationService.getTranslation(key);
    }

    ngOnInit(): void {
        this.titleService.setTitle('Sản phẩm - CUB Vietnam');
        this.metaService.updateTag({ name: 'description', content: '<PERSON>ô tả trang sản phẩm CUB Vietnam' });
    }
}
