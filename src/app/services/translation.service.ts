import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { HttpClient } from '@angular/common/http';
import { HttpClientModule } from '@angular/common/http';

type Language = 'en' | 'vi';

@Injectable({
    providedIn: 'root'
})
export class TranslationService {
    private translations: { [key: string]: string } = {};
    private currentLang = new BehaviorSubject<Language>('en');
    currentLang$ = this.currentLang.asObservable();

    constructor(private http: HttpClient) {
        const savedLang = (localStorage.getItem('language') as Language) || 'en';
        this.setLanguage(savedLang);
    }

    setLanguage(lang: Language) {
        this.currentLang.next(lang);
        localStorage.setItem('language', lang);
        this.loadTranslations(lang);
    }

    private loadTranslations(lang: Language) {
        this.http.get<{ [key: string]: string }>(`assets/locales/${lang}/translation.json`)
            .subscribe(data => {
                this.translations = data;
            });
    }

    getTranslation(key: string): string {
        console.log('translate', key, this.translations[key]);
        return this.translations[key] || key;
    }

    getCurrentLang(): Language {
        return this.currentLang.value;
    }
}
