import { Injectable, PLATFORM_ID, Inject } from '@angular/core';
import { BehaviorSubject } from 'rxjs';
import { isPlatformBrowser } from '@angular/common';
import { DOCUMENT } from '@angular/common';

@Injectable({
    providedIn: 'root'
})
export class LanguageService {
    private currentLang = new BehaviorSubject<string>('en');
    currentLang$ = this.currentLang.asObservable();

    constructor(
        @Inject(PLATFORM_ID) private platformId: Object,
        @Inject(DOCUMENT) private document: Document
    ) {
        if (isPlatformBrowser(this.platformId)) {
            const savedLang = localStorage.getItem('language') || 'en';
            this.setLanguage(savedLang, false);
        }
    }

    setLanguage(lang: string, updateStorage: boolean = true) {
        this.currentLang.next(lang);
        if (isPlatformBrowser(this.platformId)) {
            if (updateStorage) {
                localStorage.setItem('language', lang);
            }
            this.document.documentElement.lang = lang;
            this.document.documentElement.setAttribute('data-lang', lang);
        }
    }

    getCurrentLang(): string {
        return this.currentLang.value;
    }

    toggleLanguage() {
        const newLang = this.currentLang.value === 'en' ? 'vi' : 'en';
        this.setLanguage(newLang);
        return newLang;
    }
}
