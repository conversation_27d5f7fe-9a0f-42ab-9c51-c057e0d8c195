import { Routes } from '@angular/router';
import { LayoutComponent } from './components/layout/layout.component';

export const routes: Routes = [
    {
        path: '',
        component: LayoutComponent,
        children: [
            {
                path: 'home',
                loadComponent: () => import('./pages/home/<USER>').then(m => m.HomeComponent)
            },
            {
                path: 'product',
                loadComponent: () => import('./pages/product/product.component').then(m => m.ProductComponent)
            },
            {
                path: 'support',
                loadComponent: () => import('./pages/support/support.component').then(m => m.SupportComponent)
            },
            {
                path: '',
                redirectTo: 'home',
                pathMatch: 'full'
            }
        ]
    }
];
