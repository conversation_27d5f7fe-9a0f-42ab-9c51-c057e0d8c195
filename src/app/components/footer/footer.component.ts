import { Component, OnInit } from '@angular/core';
import { TranslationService } from '../../services/translation.service';

@Component({
    selector: 'app-footer',
    standalone: true,
    imports: [],
    templateUrl: './footer.component.html',
    styleUrl: './footer.component.scss'
})
export class FooterComponent implements OnInit {
    constructor(private translationService: TranslationService) { }
    
    ngOnInit() {
        console.log('footer');
        console.log(this.translationService.getTranslation('hello'));
    }

    translate(key: string): string {
        return this.translationService.getTranslation(key);
    }
}
