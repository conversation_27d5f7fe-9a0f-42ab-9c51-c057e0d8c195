import { Component, OnInit, OnD<PERSON>roy, Renderer2, ElementRef } from '@angular/core';
import { RouterLink, RouterLinkActive } from '@angular/router';
import { TranslationService } from '../../services/translation.service';

@Component({
    selector: 'app-header',
    standalone: true,
    imports: [RouterLink, RouterLinkActive],
    templateUrl: './header.component.html',
    styleUrl: './header.component.scss'
})
export class HeaderComponent implements OnInit, OnDestroy {
    currentLang: string = 'en';
    private scrollListener?: () => void;

    constructor(private translationService: TranslationService, private renderer: Renderer2, private el: ElementRef) { }

    ngOnInit() {
        this.translationService.currentLang$.subscribe(lang => {
            this.currentLang = lang;
        });
        const navbar = this.el.nativeElement.querySelector('.navbar');
        const navbarToggle = this.el.nativeElement.querySelector('#navbar-toggle');
        const navbarMenu = this.el.nativeElement.querySelector('#navbar-menu');
        this.scrollListener = this.renderer.listen('window', 'scroll', () => {
            if (window.scrollY > 10) {
                this.renderer.addClass(navbar, 'scrolled');
            } else {
                this.renderer.removeClass(navbar, 'scrolled');
            }
        });
        if (navbarToggle && navbarMenu) {
            this.renderer.listen(navbarToggle, 'click', (event: Event) => {
                if (navbarMenu.classList.contains('active')) {
                    this.renderer.removeClass(navbarMenu, 'active');
                } else {
                    this.renderer.addClass(navbarMenu, 'active');
                }
                event.stopPropagation();
            });
            this.renderer.listen('document', 'click', (event: Event) => {
                if (
                    navbarMenu.classList.contains('active') &&
                    !navbarMenu.contains(event.target) &&
                    !navbarToggle.contains(event.target)
                ) {
                    this.renderer.removeClass(navbarMenu, 'active');
                }
            });
        }
    }

    ngOnDestroy() {
        if (this.scrollListener) {
            this.scrollListener();
        }
    }

    toggleLanguage() {
        const newLang = this.currentLang === 'en' ? 'vi' : 'en';
        this.translationService.setLanguage(newLang);
    }

    translate(key: string): string {
        return this.translationService.getTranslation(key);
    }
}
