@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro.woff2') format('woff2'),
       url('../fonts/DINPro.woff') format('woff'),
       url('../fonts/DINPro.eot');
  font-weight: 400;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-Bold.woff2') format('woff2'),
       url('../fonts/DINPro-Bold.woff') format('woff'),
       url('../fonts/DINPro-Bold.eot');
  font-weight: 700;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-Medium.woff2') format('woff2'),
       url('../fonts/DINPro-Medium.woff') format('woff'),
       url('../fonts/DINPro-Medium.eot');
  font-weight: 500;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-Light.woff2') format('woff2'),
       url('../fonts/DINPro-Light.woff') format('woff'),
       url('../fonts/DINPro-Light.eot');
  font-weight: 300;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-Thin.woff2') format('woff2'),
       url('../fonts/DINPro-Thin.woff') format('woff'),
       url('../fonts/DINPro-Thin.eot');
  font-weight: 200;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-Extlight.woff2') format('woff2'),
       url('../fonts/DINPro-Extlight.woff') format('woff'),
       url('../fonts/DINPro-Extlight.eot');
  font-weight: 100;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-Black.woff2') format('woff2'),
       url('../fonts/DINPro-Black.woff') format('woff'),
       url('../fonts/DINPro-Black.eot');
  font-weight: 900;
  font-style: normal;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-Italic.woff2') format('woff2'),
       url('../fonts/DINPro-Italic.woff') format('woff'),
       url('../fonts/DINPro-Italic.eot');
  font-weight: 400;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-BoldItalic.woff2') format('woff2'),
       url('../fonts/DINPro-BoldItalic.woff') format('woff'),
       url('../fonts/DINPro-BoldItalic.eot');
  font-weight: 700;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-MediumItalic.woff2') format('woff2'),
       url('../fonts/DINPro-MediumItalic.woff') format('woff'),
       url('../fonts/DINPro-MediumItalic.eot');
  font-weight: 500;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-LightItalic.woff2') format('woff2'),
       url('../fonts/DINPro-LightItalic.woff') format('woff'),
       url('../fonts/DINPro-LightItalic.eot');
  font-weight: 300;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-ThinItalic.woff2') format('woff2'),
       url('../fonts/DINPro-ThinItalic.woff') format('woff'),
       url('../fonts/DINPro-ThinItalic.eot');
  font-weight: 200;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-ExtlightItalic.woff2') format('woff2'),
       url('../fonts/DINPro-ExtlightItalic.woff') format('woff'),
       url('../fonts/DINPro-ExtlightItalic.eot');
  font-weight: 100;
  font-style: italic;
  font-display: swap;
}
@font-face {
  font-family: 'DINPro';
  src: url('../fonts/DINPro-BlackItalic.woff2') format('woff2'),
       url('../fonts/DINPro-BlackItalic.woff') format('woff'),
       url('../fonts/DINPro-BlackItalic.eot');
  font-weight: 900;
  font-style: italic;
  font-display: swap;
}

body {
    background: #fff;
    font-family: 'DINPro', 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 0;
}

.container {
    max-width: 1440px;
    margin: 0px auto;
}

.navbar {
    position: sticky;
    top: 0;
    z-index: 100;
    background: transparent;
    transition: box-shadow 0.2s, background 0.2s;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 18px 32px 18px 32px;
    margin: 0 0 24px 0;
}

.navbar.scrolled {
    background: #fff;
    box-shadow: 0 2px 10px rgba(0,0,0,0.02);
    transition: background 0.2s, box-shadow 0.2s;
}

@supports not (position: sticky) {
    .navbar {
        position: fixed;
        width: 100%;
        left: 0;
        top: 0;
    }
}

.navbar-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.navbar-logo {
    width: 100%;
    height: 100%;
    object-fit: contain;
    margin-right: 4px;
}

.navbar-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #111;
    letter-spacing: 0.5px;
    white-space: nowrap;
}

.navbar-right {
    display: flex;
    align-items: center;
    gap: 16px;
}

.nav-btn {
    background: #fff;
    border: 1px solid #1ca14a;
    color: #111;
    font-size: 30px;
    font-weight: 600;
    border-radius: 77px;
    padding: 0 30px;
    margin-left: 0;
    cursor: pointer;
    transition: background 0.2s, color 0.2s;
    outline: none;
    display: flex;
    align-items: center;
    gap: 6px;
    height: 60px;
}

a.nav-btn{
    text-decoration: none;
}

@media (max-width: 1199.98px) {
    .nav-btn {
        width: 100%;
        align-items: center;
        display: flex;
        justify-content: center;
    }
}

.nav-btn:hover, .nav-btn:focus, .nav-btn:active {
    color: #fff;
    border-radius: 40px;
    background: linear-gradient(180deg, #72BF44 0%, #007745 100%);
}

.globe-icon {
    width: 40px;
    height: 40px;
    margin-right: 2px;
    display: inline-block;
}

.globe-icon img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.navbar-toggle {
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 44px;
    height: 44px;
    background: none;
    border: none;
    cursor: pointer;
    gap: 5px;
    margin-left: 12px;
}

.navbar-toggle span {
    display: block;
    width: 28px;
    height: 3px;
    background: #1ca14a;
    border-radius: 2px;
    transition: 0.3s;
}

@media (max-width: 1199.98px) {
    .navbar {
        margin: 0 0 24px 0;
    }

    .navbar-logo {
        width: 70%;
        height: auto;
    }

    .navbar-title {
        font-size: 1.1rem;
    }

    .navbar-right {
        position: absolute;
        top: 60px;
        right: 0;
        background: #fff;
        flex-direction: column;
        align-items: flex-end;
        gap: 8px;
        padding: 50px 70px;
        border-radius: 0 0 15px 15px;
        display: none;
        box-shadow: 0 8px 24px 0 rgba(0,0,0,0.08);
        z-index: 200;
    }

    .navbar-right.active {
        display: flex;
        align-items: center;
    }

    .navbar-toggle {
        display: flex;
    }
}

@media (max-width: 400px) {
    .navbar {
        padding: 8px 2px 8px 2px;
        margin: 0 0 16px 0;
    }

    .navbar-title {
        display: none;
    }
}

.header-container .main-banner-img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}
@media screen and (max-width: 576px) {
    .main-banner-content-home{
        padding: 0 15px
    }
}

/* start loan estimate section  */

.loan-estimate-section {
    margin-top: 100px;
}

.loan-estimate-section .header-loan {
    display: flex;
    align-items: center;
    gap: 24px;
    margin-left: 70px;
}
@media (max-width: 768px) {
    .loan-estimate-section .header-loan {
        flex-direction: column-reverse;
        margin-left: 0;
    }
}

.loan-estimate-section .title {
    background: linear-gradient(0deg, #77e3ff 0%, #58a2d9 100%);
    color: #fff;
    font-weight: 900;
    font-size: 35px;
    border-radius: 24px 24px 0 0;
    padding: 20px 32px;
    display: inline-block;
}

.loan-estimate-section .subtitle {
    font-style: italic;
    font-weight: bold;
    color: #222;
    font-size: 35px;
    margin-bottom: 20px;
}
@media (max-width: 768px) {
    .loan-estimate-section .subtitle {
        padding: 0 20px;
        text-align: center;
    }
}

.loan-estimate-section .main-content {
    display: flex;
    gap: 32px;
    border-radius: 50px;
    background: #f2f2f2;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.33);
    padding: 40px;
}

.loan-estimate-section .left,
.loan-estimate-section .right {
    flex: 1;
}

.loan-estimate-section .label {
    font-size: 35px;
    font-weight: bold;
    margin-bottom: 12px;
    color: #2e2e2e;
}

.loan-estimate-section .input-group {
    margin-bottom: 36px;
}

.loan-estimate-section .input-group-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.loan-estimate-section .input-value {
    display: inline-block;
    font-size: 40px;
    font-weight: 900;
    color: #3985c0;
    background: transparent;
    border-radius: 40px;
    padding: 8px 24px;
    margin-left: 12px;
    border: 2px solid #3985c0;
}


.loan-estimate-section .slider-container {
    margin: 16px 0 0 0;
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Modified slider styles */
.loan-estimate-section .slider {
    flex: 1;
    height: 10px;
    -webkit-appearance: none;
    appearance: none;
    background: #b9e1ff;
    border-radius: 5px;
    outline: none;
}

.loan-estimate-section .slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #3985c0;
    cursor: pointer;
    border: none;
    position: relative;
    z-index: 2;
}

.loan-estimate-section .slider::-moz-range-thumb {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #3985c0;
    cursor: pointer;
    border: none;
    position: relative;
    z-index: 2;
}

.loan-estimate-section .slider::-ms-thumb {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #3985c0;
    cursor: pointer;
    border: none;
    position: relative;
    z-index: 2;
}

.loan-estimate-section .slider {
    background: linear-gradient(to right, #3985c0 0%, #3985c0 50%, #b9e1ff 50%, #b9e1ff 100%);
    border-top: 1px solid #3985c0;
    border-bottom: 1px solid #3985c0;
    border-radius: 0;
}

.loan-estimate-section .slider-labels {
    display: flex;
    justify-content: space-between;
    font-size: 20px;
    color: #2e2e2e;
    margin-top: 2px;
    font-weight: 600;
    margin-top: 15px;
}

.loan-estimate-section .loan-term {
    font-size: 40px;
    font-weight: bold;
    color: #3985c0;
    background: transparent;
    border-radius: 40px;
    padding: 8px 24px;
    border: 2px solid #3985c0;
    margin-left: 12px;
    display: inline-block;
    font-weight: 900;
}
.loan-estimate-section .right{
    display: flex;
    flex-direction: column;
    align-items: center;
}
.loan-estimate-section .right-content {
    background: #f8fafc;
    border-radius: 24px;
    padding: 32px 24px;
    display: flex;
    flex-direction: column;
    align-items: center;
    box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
    width: 80%;
}
.loan-estimate-section .monthly-label {
    font-size: 35px;
    color: #2e2e2e;
    margin-bottom: 12px;
    text-align: center;
    font-weight: 600;
}

.loan-estimate-section .monthly-value {
    font-size: 70px;
    font-weight: 900;
    color: #3985c0;
    margin-bottom: 8px;
    text-align: center;
}

.loan-estimate-section .monthly-desc {
    font-size: 30px;
    color: #2e2e2e;
    text-align: center;
    margin-bottom: 12px;
    font-weight: 600;
}

.loan-estimate-section .note {
    font-size: 18px;
    color: #2e2e2e;
    margin: 20px 40px 0 40px;
    line-height: normal;
    font-weight: 500;
    letter-spacing: -0.5px;
}

.loan-estimate-section .note b {
    color: #222;
}

@media (max-width: 1199.98px) {
    .loan-estimate-section .main-content {
        flex-direction: column;
        gap: 0;
    }

    .loan-estimate-section .right {
        min-width: unset;
        margin-top: 24px;
    }

    .loan-estimate-section .header,
    .loan-estimate-section .main-content,
    .loan-estimate-section .note {
        padding-left: 16px;
        padding-right: 16px;
    }
}

@media (max-width: 576px) {
    .loan-estimate-section .label{
        font-size: 20px;
    }
    .loan-estimate-section .input-value {
        font-size: 25px;
    }
    .loan-estimate-section .loan-term{
        font-size: 25px;
    }
    .loan-estimate-section .monthly-value{
        font-size: 40px;
    }
    .loan-estimate-section .note{
        padding: 0;
    }
}

/* end loan estimate section  */

/* start feature section  */
.features-section{
    margin-top: 50px;
}
.features-section-content {
    display: flex;
    justify-content: space-between;
    gap: 35px;
    flex-wrap: wrap;
    margin-top: 40px;
}
@media (max-width: 1199.98px) {
    .features-section-content {
        flex-direction: column;
        padding: 0 15px;
    }
    .simple-disbursement-section .step{
        border-radius: 50px !important;
    }
}

.features-section .feature-card {
    background-color: #f0f0f0;
    padding: 30px;
    text-align: center;
    flex: 1;
    display: flex;
    flex-direction: column;
    transition: transform 0.3s ease;
    justify-content: center;
    border-radius: 30px 30px 30px 0px;
    background: #F0F0F0;
    box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.33);
}

.features-section .feature-card:hover {
    transform: translateY(-5px);
}

.features-section .icon {
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: center;
}

.features-section .icon-money {
    color: #3498db;
}

.features-section .icon-document {
    color: #9b59b6;
}

.features-section .icon-clock {
    color: #2ecc71;
}

.features-section .icon-user {
    color: #3498db;
}

.features-section .feature-title {
    font-size: 30px;
    color: #333;
    line-height: normal;
    margin-top: 10px;
}

.features-section .highlight {
    font-weight: bold;
    color: #3985c0;
}

.features-section .highlight-purple {
    font-weight: bold;
    color: #716fd0;
}

.features-section .highlight-green {
    font-weight: bold;
    color: #008f43;
}

.features-section .highlight-blue {
    font-weight: bold;
    color: #3985c0;
}

@media (max-width: 768px) {
    .features-section .features {
        flex-direction: column;
    }
}

@media (max-width: 1199.98px) {
    .features-section .feature-card {
       
    }
}
@media (max-width: 576px) {
   .features-section-content{
    flex-direction: column;
   }
   .features-section{
    padding: 0 15px;
   }
}

/* end feature section  */

/* start simple disbursement section  */
.simple-disbursement-section {
    margin-top: 130px;
}

.simple-disbursement-section .steps-container {
    border: 1px solid #716fd0;
    border-radius: 30px;
    padding: 70px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.simple-disbursement-section .title-general {
    color: #716fd0;
    text-align: center;
    font-size: 65px;
    font-weight: 900;
    margin-bottom: 70px;
    margin-top: -113px;
    background: #fff;
}

@media (max-width: 1199.98px) {
    .simple-disbursement-section{
        padding: 0 15px;
    }
}

@media (max-width: 768px) {
    .simple-disbursement-section .title-general {
        width: 90%;
        margin-top: -70px;
    }
    .simple-disbursement-section .title-general{
        font-size: 40px;
    }
}

.simple-disbursement-section .title::before,
.simple-disbursement-section .title::after {
    content: "";
    position: absolute;
    top: 50%;
    height: 1px;
    background-color: #8a7edb;
    width: 15%;
}

.simple-disbursement-section .title::before {
    left: 5%;
}

.simple-disbursement-section .title::after {
    right: 5%;
}

.simple-disbursement-section .steps-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 70px;
    row-gap: 40px;
}

.simple-disbursement-section .step {
    display: flex;
    align-items: center;
    background-color: #f2f2f2;
    border-radius: 200px;
    padding: 20px;
    position: relative;
}

.simple-disbursement-section .step-number {
    background: linear-gradient(180deg, #D4D3FF -50%, #716FD0 50%);
    color: white;
    width: 90px;
    height: 90px;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 50px;
    font-weight: 900;
    margin-right: 20px;
    flex-shrink: 0;
    position: absolute;
    top: -20px;
    left: -20px;
}

.simple-disbursement-section .step-content {
    font-size: 28px;
    color: #2e2e2e;
    text-align: left;
    padding: 0 0 0 70px;
    font-weight: 500;
}

.simple-disbursement-section .highlight {
    font-weight: 900;
}

@media (max-width: 768px) {
    .simple-disbursement-section .steps-grid {
        grid-template-columns: 1fr;
    }

    .simple-disbursement-section .title::before,
    .simple-disbursement-section .title::after {
        width: 10%;
    }

    .simple-disbursement-section .steps-container {
        padding: 20px 12px 0 30px;
        margin: 0 20px;
    }
    .simple-disbursement-section .step-content{
        font-size: 22px;
    }
    .simple-disbursement-section .steps-grid{
        row-gap: 60px;
        margin-bottom: 50px;
    }
}

/* end simple disbursement section  */

/* start app promo section  */

.app-promo-section {
    background: #ededed;
    margin-top: 100px;
}

.app-promo-section .app-promo-content {
    margin: 0 auto;
    display: flex;
    align-items: center;
    gap: 100px;
    justify-content: center;
}

.app-promo-section .app-promo-left {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: center;
    padding-left: 32px;
    min-width: 260px;
}

.app-promo-section .app-promo-left-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.app-promo-section .app-logo {
    border-radius: 24px;
    background: #fff;
    margin-bottom: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    object-fit: contain;
}

.app-promo-section .app-slogan {
    font-size: 2.2rem;
    font-weight: 800;
    color: #222;
    margin: 0 0 8px 0;
    line-height: 1.1;
}

.app-promo-section .slogan-main {
    font-size: 60px;
    font-weight: 800;
    color: #2e2e2e;
}

.app-promo-section .app-divider {
    width: 100%;
    border: none;
    border-top: 2px solid #888;
    margin: 12px 0 12px 0;
}

.app-promo-section .app-desc {
    font-size: 39px;
    font-weight: 600;
    color: #222;
    margin-bottom: 12px;
}

.app-promo-section .app-store-btns {
    display: flex;
    gap: 16px;
    margin-top: 0;
}
.app-promo-section .store-btn img{
    width: 90%;
}
.app-promo-section .store-btn.iso-icon{
    margin-left: -30px;
}


.app-promo-section .app-girl-img {
    margin-top: -40px;
    margin-bottom: -4px;
    position: relative;
}

@media (max-width: 900px) {
    .app-promo-section .app-promo-content {
        flex-direction: column;
        align-items: center;
        padding: 24px 0 16px 0;
    }

    .app-promo-section .app-promo-left {
        align-items: center;
        padding-left: 0;
        text-align: center;
    }

    .app-promo-section .app-divider {
        width: 60%;
        margin: 10px auto 10px auto;
    }

    .app-promo-section .app-promo-right {
        justify-content: center;
        margin-top: 18px;
    }

    .app-promo-section .app-girl-img {
        max-width: 100%;
    }
}

@media (max-width: 576px) {
    .app-promo-section .app-promo-left-content {
        flex-direction: column;
    }
    .app-promo-section .app-girl-img{
        margin-bottom: -20px;
    }
    .app-promo-section .slogan-main{
        font-size: 40px;
    }
    .app-promo-section .app-desc{
        font-size: 25px;
    }
}


/* end app promo section  */

/* FAQ Frequently Asked Questions section  */
.faq-section {
    margin-top: 70px;
}

.faq-section .header {
    text-align: center;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.faq-section .header h1 {
    color: #008f43;
    font-size: 65px;
    font-weight: 900;
    text-transform: uppercase;
    margin: 0;
    padding: 0;
}

.faq-section .header p {
    font-size: 22px;
    color: #333;
    margin: 0;
    padding: 0;
    font-weight: 500;
    line-height: 1;

}

.faq-section .nav-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin: 30px 0;
    flex-wrap: wrap;
}

.faq-section .nav-button {
    padding: 15px 20px;
    border-radius: 30px;
    border: none;
    font-size: 25px;
    font-weight: bold;
    cursor: pointer;
    text-align: center;
    min-width: 200px;
    color: #333;
    transition: all 0.3s ease;
}

.faq-section .nav-button.active-tab1 {
    background: linear-gradient(0deg, #72BF44 0%, #007745 100%);
    color: #fff;
}

.faq-section .nav-button.active-tab2 {
    background: linear-gradient(0deg, #D4D3FF 0%, #716FD0 100%);
    color: white;
}

.faq-section .nav-button.active-tab3 {
    background: linear-gradient(0deg, #58A2D9 0%, #77E3FF 100%);
    color: #fff;
}

.faq-section .nav-button.active-tab4 {
    background: linear-gradient(0deg, #FFD600 0%, #FF9800 100%);
    color: #fff;
}

.faq-section .nav-button:last-child.active-tab4 {
    /* Đảm bảo tab 4 luôn có màu cam khi active */
    background: linear-gradient(0deg, #7EDE45 0%, #B2E71C 100%);
    color: #fff;
}

.faq-section .nav-button.inactive {
    background-color: #d9d9d9;
}

.faq-section .nav-button:hover {
    opacity: 0.9;
    transform: translateY(-2px);
}

.faq-section .faq-container {
    background-color: #e6e6e6;
    border-radius: 20px;
    padding: 30px;
    display: flex;
    margin-top: 20px;
}

.faq-section .faq-image {
    flex: 0 0 40%;
    position: relative;
}

.faq-section .faq-image img {
    width: 100%;
    height: auto;
    border-radius: 10px;
}

.faq-section .faq-content {
    flex: 0 0 55%;
    padding-left: 50px;
}

.faq-section .tab-content {
    display: none;
}

.faq-section .tab-content.active {
    display: block;
}

.faq-section .faq-item {
    background: #F0F0F0;
    box-shadow: 0px 5px 8px 0px rgba(0, 0, 0, 0.15);
    border-radius: 30px;
    margin-bottom: 15px;
    padding: 20px;
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.faq-section .faq-item:hover {
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.faq-section .faq-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
}

.faq-section .faq-question {
    flex: 1;
    padding-right: 30px;
    font-size: 25px;
    font-weight: 500;
}

.faq-section .faq-answer {
    padding-top: 10px;
    margin-top: 15px;
    border-top: 1px solid #d9d9d9;
    color: #2e2e2e;
    font-size: 20px;
    display: none;
}

.faq-section .dropdown-icon {
    position: static;
    width: 0;
    height: 0;
    display: inline-block;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 10px solid #017845; /* tab1: xanh dương */
    transition: border-top-color 0.2s, transform 0.3s;
    right: 0;
    fill: linear-gradient(0deg, #72BF44 0%, #007745 100%);

}
.faq-section .dropdown-icon.purple {
    border-top-color: #716fd0 !important;
    background: linear-gradient(0deg, #D4D3FF 0%, #716FD0 100%);
}

.faq-section .dropdown-icon.green {
    border-top-color: #58A2D9 !important;
}

.faq-section .dropdown-icon.orange {
    border-top-color: #7EDE45 !important;
    fill: linear-gradient(0deg, #7EDE45 0%, #B2E71C 100%);
}

.faq-section .dropdown-icon.rotated {
    transform: rotate(180deg);
}

.faq-section .hotline {
    font-weight: bold;
}

.faq-section .floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.faq-section .question-mark {
    position: absolute;
    color: #4a90e2;
    font-size: 60px;
    font-weight: bold;
    top: 30%;
    right: 20%;
}

.faq-section .card-blue {
    position: absolute;
    width: 60px;
    height: 40px;
    background-color: #4a90e2;
    border-radius: 8px;
    top: 15%;
    right: 10%;
    transform: rotate(15deg);
}

.faq-section .card-purple {
    position: absolute;
    width: 60px;
    height: 40px;
    background-color: #7b68ee;
    border-radius: 8px;
    bottom: 20%;
    right: 15%;
    transform: rotate(-10deg);
}

.faq-section .book-green {
    position: absolute;
    width: 50px;
    height: 60px;
    background-color: #00a651;
    border-radius: 5px;
    bottom: 30%;
    left: 10%;
    transform: rotate(-15deg);
}
@media (max-width: 1199.98px) {
    .faq-section .faq-container{
        flex-direction: column;
        gap: 20px;
    }
    .faq-section .faq-image{
        width: 100%;
    }
    .faq-section .faq-content{
        padding-left: 0;
    }

}

@media (max-width: 768px) {
    .faq-section .faq-container {
        flex-direction: column;
    }

    .faq-section .faq-image,
    .faq-section .faq-content {
        flex: 0 0 100%;
        padding-left: 0;
    }

    .faq-section .faq-image {
        margin-bottom: 20px;
    }

    .faq-section .nav-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .faq-section .nav-button {
        width: 100%;
    }
    .faq-section .nav-buttons{
        padding: 0 20px;
    }
}

@media (max-width: 576px) {
    .faq-section .header h1{
        font-size: 40px;
    }
    .faq-section .header{
        padding: 0 20px;
    }
}

/* end FAQ Frequently Asked Questions section  */
.footer-section {
    margin-top: 70px;
    background: linear-gradient(180deg, #ededed 80%, #f7f7f7 100%);
    border-radius: 48px 48px 0 0;
    margin-bottom: 0;
    padding: 0 30px;
    min-height: 120px;
    box-sizing: border-box;
}

.footer-content {
    margin: 0 auto;
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
    padding: 40px 0;
    gap: 40px;
    flex-wrap: wrap;
}

.footer-col {
    margin-bottom: 12px;
    color: #222;
    font-size: 1.08rem;
}

.footer-logo-col {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
}

.footer-logo {
    width: 90%;
    height: 100%;
    object-fit: contain;
}

.footer-bank-name {
    font-size: 1.1rem;
    font-weight: 500;
    margin-bottom: 2px;
}

.footer-bank-branch p {
    font-size: 33px;
    font-weight: 800;
    margin-bottom: 0;
    text-transform: uppercase;
    padding: 0;
    margin: 0;
}

.footer-bank-branch span {
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 0;
}

.footer-address-col {
    font-size: 18px;
    font-weight: 500;
}

.footer-social-col {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    margin-top: 8px;
}

.footer-social-title {
    font-size: 1.08rem;
    font-weight: 700;
    margin-bottom: 4px;
}

.footer-social-icons {
    display: flex;
    gap: 5px;
}

.footer-social-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 1.5px solid #1ca14a;
    border-radius: 50%;
    background: transparent;
    transition: background 0.2s, border 0.2s;
    margin-right: 0;
    text-decoration: none;
}

.footer-social-icon img {
    width: 20px;
    height: 20px;
    filter: invert(38%) sepia(97%) saturate(469%) hue-rotate(92deg) brightness(92%) contrast(92%);
}

.footer-social-icon:hover {
    background: #e6f7ec;
    border-color: #7b6ee6;
}

.footer-contact-col {
    display: flex;
    flex-direction: column;
    gap: 8px;
    margin-top: 8px;
    font-size: 1.08rem;
}

.footer-contact-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 18px;
    font-weight: 500;
}

.footer-contact-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    border: 2px solid #1ca14a;
    border-radius: 50%;
    color: #1ca14a;
    font-size: 1.1rem;
    background: #fff;
    margin-right: 6px;
    flex-shrink: 0;
    text-align: center;
    justify-content: center;
}

.footer-contact-note {
    font-size: 0.95rem;
    color: #888;
    margin-top: 2px;
}

.time-work {
    line-height: 5px;
}

@media (max-width: 1199.98px) {
    .footer-content {
        flex-direction: column;
        align-items: flex-end;
        padding: 40px 20px;
        gap: 8px;
    }

    .footer-col {
        min-width: 0;
        width: 100%;
        margin-bottom: 10px;
    }

    .footer-social-icons {
        gap: 10px;
    }

    .footer-social-icon {
        width: 32px;
        height: 32px;
    }

    .footer-social-icon img {
        width: 18px;
        height: 18px;
    }

    .footer-contact-icon {
        width: 20px;
        height: 20px;
        font-size: 1rem;
    }
}


/* header sản phẩm */
.header-container-sp{
    position: relative;
}
.main-banner-content{
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
}

.main-banner-img-container img{
    max-width: 100%;
    height: auto;
}
@media (max-width: 1199.98px) {
    .header{
        margin-top: 0 !important;
        text-align: center;
    }
    .main-banner-content{
        display: block;
    }
    .main-banner-img-container img{
        max-width: 100%;
        height: auto;
    }
}
.header-container-sp .header {
    margin-top: -300px;
}
.header-container-sp .header h1 {
    color: #7b68ee;
    font-size: 55px;
    font-weight: 900;
    padding: 0;
    margin: 0;
    white-space: nowrap;
    display: inline-block;
}
.header-container-sp .header h2 {
    color: #222;
    font-size: 50px;
    font-weight: 800;
    margin-bottom: 10px;
    padding: 0;
    margin: 10px 0 0 0;
}
.main-banner-content::before{
    position: absolute;
    width: 100%;
    height: 300px;
    content: "";
    bottom: 0;
    z-index: 1;
    background: linear-gradient(180deg, rgba(236, 243, 250, 0.00) 24.31%, #F6F6F6 64.09%, #F6F6F6 100%);
}

@media (max-width: 1199.98px) {
    .header-container-sp .header h1{
        font-size: 40px;
    }
    .header-container-sp .header h2{
        font-size: 35px;
        margin-top: 0;
    }
}

@media (max-width: 576px) {
    .header-container-sp .header h1{
        font-size: 30px;
    }
    .header-container-sp .header h2{
        font-size: 25px;
        margin-top: 0;
    }
}

/* end header sản phẩm */

/* start sản phẩm */

.section-sp{
    position: relative;
    z-index: 2;
}
.section-sp .product-row {
    display: flex;
    justify-content: space-between;
    gap: 24px;
    margin-top: -160px;
    flex-wrap: wrap;
}
.section-sp .product-col {
    background: #fff;
    border-radius: 20px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    flex: 1 1 300px;
    min-width: 300px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
}
.section-sp .product-col.green {
    border-radius: 30px;
    border: 1px solid #008F43;
    background: #FFF;
    box-shadow: 0px 3px 0px 12px rgba(248, 248, 248, 0.55) inset, 4px 5px 29px 0px rgba(0, 0, 0, 0.35);
}
.section-sp .product-col.blue {
    border-radius: 30px;
    border: 1px solid #4a90e2;
    background: #FFF;
    box-shadow: 0px 3px 0px 12px rgba(248, 248, 248, 0.55) inset, 4px 5px 29px 0px rgba(0, 0, 0, 0.35);
}
.section-sp .product-col.purple {
    border-radius: 30px;
    border: 1px solid #7b68ee;
    background: #FFF;
    box-shadow: 0px 3px 0px 12px rgba(248, 248, 248, 0.55) inset, 4px 5px 29px 0px rgba(0, 0, 0, 0.35);
}
.section-sp .product-title {
    font-size: 35px;
    font-weight: bold;
    margin-bottom: 18px;
}
.section-sp .product-title.green {
    color: #fff;
    background: linear-gradient(180deg, #72BF44 -50%, #007745 50%);
    width: 100%;
    padding: 20px 0;
    margin: 40px 0 20px 0 ;
    text-align: center;
}
.section-sp .product-title.blue {
    color: #fff;
    background: linear-gradient(180deg, #58A2D9 -50%, #77E3FF 50%);
    width: 100%;
    padding: 20px 0;
    margin: 40px 0 20px 0 ;
    text-align: center;
}
.section-sp .product-title.purple {
    color: #fff;
    background: linear-gradient(180deg, #D4D3FF -50%, #716FD0 50%);
    width: 100%;
    padding: 20px 0;
    margin: 40px 0 20px 0 ;
    text-align: center;
}
.section-sp .product-list {
    list-style: none;
    padding: 0 30px;
}
.section-sp .product-list li {
    display: flex;
    align-items: flex-start;
    gap: 10px;
    margin-bottom: 20px;
}

.section-sp .product-list .product-list-item p{
    font-size: 30px;
    padding: 0;
    margin: 0;
    font-weight: 600;
}
.section-sp .note-green{
    font-size: 12px;
    line-height: normal;

}
.section-sp .note-green a{
    color: #00a651;
    text-decoration: none;
}

.section-sp .product-list li:last-child {
    margin-bottom: 30px;
}
.section-sp .checkmark {
    color: #00a651;
    font-size: 20px;
    margin-top: 2px;
}
.section-sp .highlight-green {
    color: #00a651;
    font-weight: bold;
}
.section-sp .highlight-blue {
    color: #4a90e2;
    font-weight: bold;
}
.section-sp .highlight-purple {
    color: #7b68ee;
    font-weight: bold;
}



@media (max-width: 1199.98px) {
    .section-sp .product-row {
        flex-direction: column;
        gap: 18px;
    }
}
/* end sản phẩm */

/* start ai có thể đăng ký */
.register-section{
    margin-top: 100px;
    text-align: center;
}
.register-section .register-title{
    color:#58a2d9;
    font-size:70px;
    font-weight:bold;
    margin-bottom:36px;
}
.register-section .register-conditions{
    display:flex;
    justify-content:space-around;
    gap:40px;
    flex-wrap:wrap;
    margin-bottom:36px;
}
.register-section .register-col{
    display:flex;
    flex-direction:column;
    align-items:center;
    gap:10px;
    position: relative;
}

.register-section .register-col-line{
    width: 1px;
    height: 450px;
    background: #008f43;
}
@media (max-width: 1199.98px) {
    .register-section .register-col-line{
        width: 90%;
        height: 1px;
        background: #008f43;
        margin: 50px auto;
    }
    .register-section .register-conditions{
        display: block !important;
    }
    .section-sp .product-col{
        margin: 0 20px;
    }
    .register-section .register-title{
        font-size: 60px;
        padding: 0 20px;
    }
   
}

/* end ai có thể đăng ký */

/* tải app */
.app-download-section{
    margin-top: 100px;
    text-align: center;
}
.app-download-content{
    display:flex;
    flex-direction:column;
    align-items:center;
}
.app-download-content-item{
    display:flex;
    align-items:center;
    gap:50px;
    margin-bottom:8px;
}
.app-download-content-item-content{
    display:flex;
    flex-direction:column;
    align-items:center; gap:10px;
}
.app-download-content-item-content-title{
    font-size:42px;
    font-weight:600;
    color:#222;
    letter-spacing:0.5px;
}
.app-download-content-item-content-app-store{
    display:flex; gap:16px;
}

@media (max-width: 1199.98px) {
    .app-download-section{
        padding: 0 20px;
    }
    .app-download-content-item{
        display: block;
    }
    .app-download-content-item-content-app-store img{
        max-width: 100%;
        height: auto !important;
    }
}
/* end tải app   */

.vnd-black {
  color: #2e2e2e !important;
}

.vnd-blue {
  color: #3985c0 !important;
}

.section-support{
    display: block;
    width: 100%;
    margin-top: 20px;
}
.section-support .support-content{
    display: block;
    width: 100%;
    padding: 30px;
    border-radius: 30px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.06);
    font-size: 24px;
    word-break: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    box-sizing: border-box;
    overflow: auto;
}
@media screen and (max-width: 1199.98px) {
    .section-support .support-content{
        padding: 20px;
    }
}
@media screen and (max-width: 576px) {
    .section-support .support-content{
        padding: 10px;
        font-size: 16px;
    }
}
