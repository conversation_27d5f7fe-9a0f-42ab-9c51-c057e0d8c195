@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro.woff2') format('woff2'), url('../fonts/DINPro.woff') format('woff'), url('../fonts/DINPro.eot');
	font-weight: 400;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-Bold.woff2') format('woff2'), url('../fonts/DINPro-Bold.woff') format('woff'), url('../fonts/DINPro-Bold.eot');
	font-weight: 700;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-Medium.woff2') format('woff2'), url('../fonts/DINPro-Medium.woff') format('woff'), url('../fonts/DINPro-Medium.eot');
	font-weight: 500;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-Light.woff2') format('woff2'), url('../fonts/DINPro-Light.woff') format('woff'), url('../fonts/DINPro-Light.eot');
	font-weight: 300;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-Thin.woff2') format('woff2'), url('../fonts/DINPro-Thin.woff') format('woff'), url('../fonts/DINPro-Thin.eot');
	font-weight: 200;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-Extlight.woff2') format('woff2'), url('../fonts/DINPro-Extlight.woff') format('woff'), url('../fonts/DINPro-Extlight.eot');
	font-weight: 100;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-Black.woff2') format('woff2'), url('../fonts/DINPro-Black.woff') format('woff'), url('../fonts/DINPro-Black.eot');
	font-weight: 900;
	font-style: normal;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-Italic.woff2') format('woff2'), url('../fonts/DINPro-Italic.woff') format('woff'), url('../fonts/DINPro-Italic.eot');
	font-weight: 400;
	font-style: italic;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-BoldItalic.woff2') format('woff2'), url('../fonts/DINPro-BoldItalic.woff') format('woff'), url('../fonts/DINPro-BoldItalic.eot');
	font-weight: 700;
	font-style: italic;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-MediumItalic.woff2') format('woff2'), url('../fonts/DINPro-MediumItalic.woff') format('woff'), url('../fonts/DINPro-MediumItalic.eot');
	font-weight: 500;
	font-style: italic;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-LightItalic.woff2') format('woff2'), url('../fonts/DINPro-LightItalic.woff') format('woff'), url('../fonts/DINPro-LightItalic.eot');
	font-weight: 300;
	font-style: italic;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-ThinItalic.woff2') format('woff2'), url('../fonts/DINPro-ThinItalic.woff') format('woff'), url('../fonts/DINPro-ThinItalic.eot');
	font-weight: 200;
	font-style: italic;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-ExtlightItalic.woff2') format('woff2'), url('../fonts/DINPro-ExtlightItalic.woff') format('woff'), url('../fonts/DINPro-ExtlightItalic.eot');
	font-weight: 100;
	font-style: italic;
	font-display: swap;
}

@font-face {
	font-family: 'DINPro';
	src: url('../fonts/DINPro-BlackItalic.woff2') format('woff2'), url('../fonts/DINPro-BlackItalic.woff') format('woff'), url('../fonts/DINPro-BlackItalic.eot');
	font-weight: 900;
	font-style: italic;
	font-display: swap;
}

/* start loan estimate section  */
/* Modified slider styles */
/* end loan estimate section  */
/* start feature section  */
/* end feature section  */
/* start simple disbursement section  */
/* end simple disbursement section  */
/* start app promo section  */
/* end app promo section  */
/* FAQ Frequently Asked Questions section  */
/* end FAQ Frequently Asked Questions section  */
/* header sản phẩm */
/* end header sản phẩm */
/* start sản phẩm */
/* end sản phẩm */
/* start ai có thể đăng ký */
/* end ai có thể đăng ký */
/* tải app */
/* end tải app   */
body {
	background: #fff;
	font-family: 'DINPro', 'Segoe UI', Arial, sans-serif;
	margin: 0;
	padding: 0;
}
.pg-pc{
    display: block ! important;
}
.pg-mobile{
    display: none ! important;
}
@media screen and (max-width: 768px) {
    .pg-pc{
        display: none ! important;
    }
    .pg-mobile{
        display: block ! important;
    }
}
.table-wrapper {
	width: 100%;
	overflow-x: auto;
	-webkit-overflow-scrolling: touch;
	border: 1px solid #ddd;
	border-radius: 4px;

	table {
		width: 100%;
		min-width: 800px; // 👈 quan trọng để table có thể scroll nếu screen nhỏ
		border-collapse: collapse;
		font-family: Arial, sans-serif;
		thead {
			th {
				text-align: center;
				padding: 12px 10px;
				font-weight: bold;
				border: 1px solid #ccc;
				white-space: nowrap;
				background-color: #f8f8f8;
			}
		}

		tbody {

			td,
			th {
				padding: 10px;
				border: 1px solid #ccc;
				vertical-align: top;
				text-align: left;
				white-space: normal;
			}

			tr:nth-child(even) {
				background-color: #f9f9f9;
			}

			tr:hover {
				background-color: #f1f1f1;
			}

			td:first-child,
			th:first-child {
				text-align: center;
				width: 40px;
				white-space: nowrap;
			}
		}
	}
}

.container {
	max-width: 1440px;
	margin: 0px auto;
}

.navbar {
	position: sticky;
	top: 0;
	z-index: 100;
	background: transparent;
	transition: box-shadow 0.2s, background 0.2s;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 18px 32px 18px 32px;
	margin: 0 0 24px 0;
}

.navbar.scrolled {
	background: #fff;
	box-shadow: 0 2px 10px rgba(0, 0, 0, 0.02);
	transition: background 0.2s, box-shadow 0.2s;
}

@supports not (position: sticky) {
	.navbar {
		position: fixed;
		width: 100%;
		left: 0;
		top: 0;
	}
}

a {
	text-decoration: none;
	color: #111;

	&:hover {
		color: #017845;
	}
}

.navbar-left {
	display: flex;
	align-items: center;
	gap: 12px;
}

.navbar-logo {
	width: 100%;
	height: 100%;
	object-fit: contain;
	margin-right: 4px;
}

.navbar-title {
	font-size: 1.5rem;
	font-weight: 600;
	color: #111;
	letter-spacing: 0.5px;
	white-space: nowrap;
}

.navbar-right {
	display: flex;
	align-items: center;
	gap: 16px;
}

.nav-btn {
	background: #fff;
	border: 1px solid #1ca14a;
	color: #111;
	font-size: 30px;
	font-weight: 600;
	border-radius: 77px;
	padding: 0 30px;
	margin-left: 0;
	cursor: pointer;
	transition: background 0.2s, color 0.2s;
	outline: none;
	display: flex;
	align-items: center;
	gap: 6px;
	height: 60px;

	&:hover {
		color: #fff;
		border-radius: 40px;
		background: linear-gradient(180deg, #72BF44 0%, #007745 100%);
	}

	&:focus {
		color: #fff;
		border-radius: 40px;
		background: linear-gradient(180deg, #72BF44 0%, #007745 100%);
	}

	&:active {
		color: #fff;
		border-radius: 40px;
		background: linear-gradient(180deg, #72BF44 0%, #007745 100%);
	}
}

a.nav-btn {
	text-decoration: none;
}

.globe-icon {
	width: 40px;
	height: 40px;
	margin-right: 2px;
	display: inline-block;

	img {
		width: 100%;
		height: 100%;
		object-fit: contain;
	}
}

.navbar-toggle {
	display: none;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	width: 44px;
	height: 44px;
	background: none;
	border: none;
	cursor: pointer;
	gap: 5px;
	margin-left: 12px;

	span {
		display: block;
		width: 28px;
		height: 3px;
		background: #1ca14a;
		border-radius: 2px;
		transition: 0.3s;
	}
}
.main-banner-content-home{
	position: relative;
	.link-banner{
		display: inline-block;
		position: absolute;
		bottom: 9%;
		width: 4.4%;
		height: 12%;
		&.icon-app-ios{
			left: 17%;
		}
		&.icon-app-android{
			left: 22.5%;
		}
	}
    @media screen and (max-width: 576px) {
        .link-banner{
            width: 7%;
            height: 8%;
            &.icon-app-ios{
                left: 10%;
            }
            &.icon-app-android{
                left: 18.5%;
            }
        }
        
    }
}
.header-container {
	.main-banner-img {
		width: 100%;
		height: 100%;
		object-fit: cover;
	}
}

.loan-estimate-section {
	margin-top: 100px;

	.header-loan {
		display: flex;
		align-items: center;
		gap: 24px;
		margin-left: 70px;
	}

	.title {
		background: linear-gradient(0deg, #77e3ff 0%, #58a2d9 100%);
		color: #fff;
		font-weight: 900;
		font-size: 35px;
		border-radius: 24px 24px 0 0;
		padding: 20px 32px;
		display: inline-block;
	}

	.subtitle {
		font-style: italic;
		font-weight: bold;
		color: #222;
		font-size: 35px;
		margin-bottom: 20px;
	}

	.main-content {
		display: flex;
		gap: 32px;
		border-radius: 50px;
		background: #f2f2f2;
		box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.33);
		padding: 40px;
	}

	.left {
		flex: 1;
	}

	.right {
		flex: 1;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.label {
		font-size: 35px;
		font-weight: bold;
		margin-bottom: 12px;
		color: #2e2e2e;
	}

	.input-group {
		margin-bottom: 36px;
	}

	.input-group-content {
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.input-value {
		display: inline-block;
		font-size: 40px;
		font-weight: 900;
		color: #3985c0;
		background: transparent;
		border-radius: 40px;
		padding: 8px 24px;
		margin-left: 12px;
		border: 2px solid #3985c0;
	}

	.slider-container {
		margin: 16px 0 0 0;
		display: flex;
		align-items: center;
		gap: 12px;
	}

	.slider {
		flex: 1;
		height: 10px;
		-webkit-appearance: none;
		appearance: none;
		background: #b9e1ff;
		border-radius: 5px;
		outline: none;
		background: linear-gradient(to right, #3985c0 0%, #3985c0 50%, #b9e1ff 50%, #b9e1ff 100%);
		border-top: 1px solid #3985c0;
		border-bottom: 1px solid #3985c0;
		border-radius: 0;

		&::-webkit-slider-thumb {
			-webkit-appearance: none;
			appearance: none;
			width: 30px;
			height: 30px;
			border-radius: 50%;
			background: #3985c0;
			cursor: pointer;
			border: none;
			position: relative;
			z-index: 2;
		}

		&::-moz-range-thumb {
			width: 30px;
			height: 30px;
			border-radius: 50%;
			background: #3985c0;
			cursor: pointer;
			border: none;
			position: relative;
			z-index: 2;
		}

		&::-ms-thumb {
			width: 30px;
			height: 30px;
			border-radius: 50%;
			background: #3985c0;
			cursor: pointer;
			border: none;
			position: relative;
			z-index: 2;
		}
	}

	.slider-labels {
		display: flex;
		justify-content: space-between;
		font-size: 20px;
		color: #2e2e2e;
		margin-top: 2px;
		font-weight: 600;
		margin-top: 15px;
	}

	.loan-term {
		font-size: 40px;
		font-weight: bold;
		color: #3985c0;
		background: transparent;
		border-radius: 40px;
		padding: 8px 24px;
		border: 2px solid #3985c0;
		margin-left: 12px;
		display: inline-block;
		font-weight: 900;
	}

	.right-content {
		background: #f8fafc;
		border-radius: 24px;
		padding: 32px 24px;
		display: flex;
		flex-direction: column;
		align-items: center;
		box-shadow: 0 2px 8px rgba(33, 150, 243, 0.04);
		width: 80%;
	}

	.monthly-label {
		font-size: 35px;
		color: #2e2e2e;
		margin-bottom: 12px;
		text-align: center;
		font-weight: 600;
	}

	.monthly-value {
		font-size: 70px;
		font-weight: 900;
		color: #3985c0;
		margin-bottom: 8px;
		text-align: center;
	}

	.monthly-desc {
		font-size: 30px;
		color: #2e2e2e;
		text-align: center;
		margin-bottom: 12px;
		font-weight: 600;
	}

	.note {
		font-size: 18px;
		color: #2e2e2e;
		margin: 20px 40px 0 40px;
		line-height: normal;
		font-weight: 500;
		letter-spacing: -0.5px;

		b {
			color: #222;
		}
	}
}

.features-section {
	margin-top: 50px;

	.features-section-content {
		display: grid;
		grid-template-columns: repeat(4, 1fr);
		gap: 35px;
		margin-top: 40px;

		.feature-card {
			background-color: #f0f0f0;
			padding: 30px;
			text-align: center;
			display: flex;
			flex-direction: column;
			justify-content: center;
			transition: transform 0.3s ease;
			border-radius: 30px 30px 30px 0px;
			background: #f0f0f0;
			box-shadow: 0px 4px 8px rgba(0, 0, 0, 0.33);
			max-width: 100%; // ✅ sửa từ 100px → 100% cho auto-fit

			&:hover {
				transform: translateY(-5px);
			}
		}

		.icon {
			margin: 0 auto;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.icon-money {
			color: #3498db;
		}

		.icon-document {
			color: #9b59b6;
		}

		.icon-clock {
			color: #2ecc71;
		}

		.icon-user {
			color: #3498db;
		}

		.feature-title {
			font-size: 20px;
			color: #333;
			line-height: normal;
			margin-top: 10px;
		}

		.highlight,
		.highlight-blue {
			font-weight: bold;
			color: #3985c0;
		}

		.highlight-purple {
			font-weight: bold;
			color: #716fd0;
		}

		.highlight-green {
			font-weight: bold;
			color: #008f43;
		}



		@media (max-width: 768px) {
			grid-template-columns: repeat(2, 1fr);
			gap: 15px;
		}

		@media (max-width: 576px) {
			grid-template-columns: repeat(2, 1fr); // Mobile rất nhỏ thì nên 1 cột
			gap: 15px;

			.feature-title {
				font-size: 16px;
			}

			&.option-2 {
				grid-template-columns: repeat(1, 1fr);
				gap: 15px;

				.feature-card {
					box-sizing: border-box;
					width: 300px;
					margin: 0 auto;
				}
			}
		}
	}
}

.simple-disbursement-section {
	margin-top: 130px;

	.steps-container {
		border: 1px solid #716fd0;
		border-radius: 30px;
		padding: 70px;
		text-align: center;
		display: flex;
		flex-direction: column;
		align-items: center;
	}

	.title-general {
		color: #716fd0;
		text-align: center;
		font-size: 65px;
		font-weight: 900;
		margin-bottom: 70px;
		margin-top: -113px;
		background: #fff;
	}

	.title {
		&::before {
			content: "";
			position: absolute;
			top: 50%;
			height: 1px;
			background-color: #8a7edb;
			width: 15%;
			left: 5%;
		}

		&::after {
			content: "";
			position: absolute;
			top: 50%;
			height: 1px;
			background-color: #8a7edb;
			width: 15%;
			right: 5%;
		}
	}

	.steps-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 70px;
		row-gap: 40px;
	}

	.step {
		display: flex;
		align-items: center;
		background-color: #f2f2f2;
		border-radius: 200px;
		padding: 20px;
		position: relative;
	}

	.step-number {
		background: linear-gradient(180deg, #D4D3FF -50%, #716FD0 50%);
		color: white;
		width: 90px;
		height: 90px;
		border-radius: 50%;
		display: flex;
		justify-content: center;
		align-items: center;
		font-size: 50px;
		font-weight: 900;
		margin-right: 20px;
		flex-shrink: 0;
		position: absolute;
		top: -20px;
		left: -20px;
	}

	.step-content {
		font-size: 28px;
		color: #2e2e2e;
		text-align: left;
		padding: 0 0 0 70px;
		font-weight: 500;
	}

	.highlight {
		font-weight: 900;
	}
}

.app-promo-section {
	background: #ededed;
	margin-top: 100px;

	.app-promo-content {
		margin: 0 auto;
		display: flex;
		align-items: center;
		gap: 100px;
		justify-content: center;
	}

	.app-promo-left {
		display: flex;
		flex-direction: column;
		align-items: flex-start;
		justify-content: center;
		padding-left: 32px;
		min-width: 260px;
	}

	.app-promo-left-content {
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 20px;
	}

	.app-logo {
		border-radius: 24px;
		background: #fff;
		margin-bottom: 8px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
		object-fit: contain;
	}

	.app-slogan {
		font-size: 2.2rem;
		font-weight: 800;
		color: #222;
		margin: 0 0 8px 0;
		line-height: 1.1;
	}

	.slogan-main {
		font-size: 60px;
		font-weight: 800;
		color: #2e2e2e;
	}

	.app-divider {
		width: 100%;
		border: none;
		border-top: 2px solid #888;
		margin: 12px 0 12px 0;
	}

	.app-desc {
		font-size: 39px;
		font-weight: 600;
		color: #222;
		margin-bottom: 12px;
	}

	.app-store-btns {
		display: flex;
		gap: 16px;
		margin-top: 0;
	}

	.store-btn {
		img {
			width: 90%;
		}
	}

	.store-btn.iso-icon {
		margin-left: -30px;
	}

	.app-girl-img {
		margin-top: -40px;
		margin-bottom: -4px;
		position: relative;
	}
}

.faq-section {
	margin-top: 70px;

	.header {
		text-align: center;
		display: flex;
		flex-direction: column;
		gap: 10px;

		h1 {
			color: #008f43;
			font-size: 65px;
			font-weight: 900;
			text-transform: uppercase;
			margin: 0;
			padding: 0;
		}

		p {
			font-size: 22px;
			color: #333;
			margin: 0;
			padding: 0;
			font-weight: 500;
			line-height: 1;
		}
	}

	.nav-buttons {
		display: flex;
		justify-content: center;
		gap: 15px;
		margin: 30px 0;
		flex-wrap: wrap;
	}

	.nav-button {
		padding: 15px 20px;
		border-radius: 30px;
		border: none;
		font-size: 25px;
		font-weight: bold;
		cursor: pointer;
		text-align: center;
		min-width: 200px;
		color: #333;
		transition: all 0.3s ease;

		&:last-child.active-tab4 {
			background: linear-gradient(0deg, #7EDE45 0%, #B2E71C 100%);
			color: #fff;
		}

		&:hover {
			opacity: 0.9;
			transform: translateY(-2px);
		}
	}

	.nav-button.active-tab1 {
		background: linear-gradient(0deg, #72BF44 0%, #007745 100%);
		color: #fff;
	}

	.nav-button.active-tab2 {
		background: linear-gradient(0deg, #D4D3FF 0%, #716FD0 100%);
		color: white;
	}

	.nav-button.active-tab3 {
		background: linear-gradient(0deg, #58A2D9 0%, #77E3FF 100%);
		color: #fff;
	}

	.nav-button.active-tab4 {
		background: linear-gradient(0deg, #FFD600 0%, #FF9800 100%);
		color: #fff;
	}

	.nav-button.inactive {
		background-color: #d9d9d9;
	}

	.faq-container {
		background-color: #e6e6e6;
		border-radius: 20px;
		padding: 30px;
		display: flex;
		margin-top: 20px;
	}

	.faq-image {
		flex: 0 0 40%;
		position: relative;

		img {
			width: 100%;
			height: auto;
			border-radius: 10px;
		}
	}

	.faq-content {
		flex: 0 0 55%;
		padding-left: 50px;
	}

	.tab-content {
		display: none;
	}

	.tab-content.active {
		display: block;
	}

	.faq-item {
		background: #F0F0F0;
		box-shadow: 0px 5px 8px 0px rgba(0, 0, 0, 0.15);
		border-radius: 30px;
		margin-bottom: 15px;
		padding: 20px;
		position: relative;
		cursor: pointer;
		transition: all 0.3s ease;

		&:hover {
			box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
		}
	}

	.faq-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		position: relative;
	}

	.faq-question {
		flex: 1;
		padding-right: 30px;
		font-size: 25px;
		font-weight: 500;
	}

	.faq-answer {
		padding-top: 10px;
		margin-top: 15px;
		border-top: 1px solid #d9d9d9;
		color: #2e2e2e;
		font-size: 20px;
		display: none;
	}

	.dropdown-icon {
		position: static;
		width: 0;
		height: 0;
		display: inline-block;
		border-left: 10px solid transparent;
		border-right: 10px solid transparent;
		border-top: 10px solid #017845;
		transition: border-top-color 0.2s, transform 0.3s;
		right: 0;
		fill: linear-gradient(0deg, #72BF44 0%, #007745 100%);
	}

	.dropdown-icon.purple {
		border-top-color: #716fd0 !important;
		background: linear-gradient(0deg, #D4D3FF 0%, #716FD0 100%);
	}

	.dropdown-icon.green {
		border-top-color: #58A2D9 !important;
	}

	.dropdown-icon.orange {
		border-top-color: #7EDE45 !important;
		fill: linear-gradient(0deg, #7EDE45 0%, #B2E71C 100%);
	}

	.dropdown-icon.rotated {
		transform: rotate(180deg);
	}

	.hotline {
		font-weight: bold;
	}

	.floating-elements {
		position: absolute;
		width: 100%;
		height: 100%;
		top: 0;
		left: 0;
	}

	.question-mark {
		position: absolute;
		color: #4a90e2;
		font-size: 60px;
		font-weight: bold;
		top: 30%;
		right: 20%;
	}

	.card-blue {
		position: absolute;
		width: 60px;
		height: 40px;
		background-color: #4a90e2;
		border-radius: 8px;
		top: 15%;
		right: 10%;
		transform: rotate(15deg);
	}

	.card-purple {
		position: absolute;
		width: 60px;
		height: 40px;
		background-color: #7b68ee;
		border-radius: 8px;
		bottom: 20%;
		right: 15%;
		transform: rotate(-10deg);
	}

	.book-green {
		position: absolute;
		width: 50px;
		height: 60px;
		background-color: #00a651;
		border-radius: 5px;
		bottom: 30%;
		left: 10%;
		transform: rotate(-15deg);
	}
}

.footer-section {
	margin-top: 70px;
	background: linear-gradient(180deg, #ededed 80%, #f7f7f7 100%);
	border-radius: 48px 48px 0 0;
	margin-bottom: 0;
	padding: 0 30px;
	min-height: 120px;
	box-sizing: border-box;
}

.footer-content {
	margin: 0 auto;
	display: flex;
	align-items: flex-end;
	justify-content: space-between;
	padding: 40px 0;
	gap: 40px;
	flex-wrap: wrap;
}

.footer-col {
	margin-bottom: 12px;
	color: #222;
	font-size: 1.08rem;
}

.footer-logo-col {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 20px;
}

.footer-logo {
	width: 90%;
	height: 100%;
	object-fit: contain;
}

.footer-bank-name {
	font-size: 1.1rem;
	font-weight: 500;
	margin-bottom: 2px;
}

.footer-bank-branch {
	p {
		font-size: 33px;
		font-weight: 800;
		margin-bottom: 0;
		text-transform: uppercase;
		padding: 0;
		margin: 0;
	}

	span {
		font-size: 24px;
		font-weight: 500;
		margin-bottom: 0;
	}
}

.footer-address-col {
	font-size: 18px;
	font-weight: 500;
}

.footer-social-col {
	display: flex;
	flex-direction: column;
	align-items: flex-start;
	gap: 8px;
	margin-top: 8px;
}

.footer-social-title {
	font-size: 1.08rem;
	font-weight: 700;
	margin-bottom: 4px;
}

.footer-social-icons {
	display: flex;
	gap: 5px;
}

.footer-social-icon {
	display: flex;
	align-items: center;
	justify-content: center;
	width: 40px;
	height: 40px;
	border: 1.5px solid #1ca14a;
	border-radius: 50%;
	background: transparent;
	transition: background 0.2s, border 0.2s;
	margin-right: 0;
	text-decoration: none;

	img {
		width: 20px;
		height: 20px;
		filter: invert(38%) sepia(97%) saturate(469%) hue-rotate(92deg) brightness(92%) contrast(92%);
	}

	&:hover {
		background: #e6f7ec;
		border-color: #7b6ee6;
	}
}

.footer-contact-col {
	display: flex;
	flex-direction: column;
	gap: 8px;
	margin-top: 8px;
	font-size: 1.08rem;
}

.footer-contact-item {
	display: flex;
	align-items: center;
	gap: 8px;
	font-size: 18px;
	font-weight: 500;
}

.footer-contact-icon {
	display: inline-flex;
	align-items: center;
	justify-content: center;
	width: 24px;
	height: 24px;
	border: 2px solid #1ca14a;
	border-radius: 50%;
	color: #1ca14a;
	font-size: 1.1rem;
	background: #fff;
	margin-right: 6px;
	flex-shrink: 0;
	text-align: center;
	justify-content: center;
}

.footer-contact-note {
	font-size: 0.95rem;
	color: #888;
	margin-top: 2px;
}

.time-work {
	line-height: 5px;
    @media screen and (max-width: 768px) {
        line-height: 1;
    }
}

.header-container-sp {
	position: relative;

	.header {
		margin-top: -300px;

		h1 {
			color: #7b68ee;
			font-size: 55px;
			font-weight: 900;
			padding: 0;
			margin: 0;
			white-space: nowrap;
			display: inline-block;
		}

		h2 {
			color: #222;
			font-size: 50px;
			font-weight: 800;
			margin-bottom: 10px;
			padding: 0;
			margin: 10px 0 0 0;
		}
		.download-app {
			display: block;
			font-size: 30px;
			text-align: center;
			font-weight: bold;
			margin-top: 40px;
			margin-bottom: 20px;
			.download-app-icon {
				display: flex;
				flex-flow: row nowrap;
				justify-content: center;
				a{
					display: inline-block;
					img {
						display: inline-block;
					}
				}
			}
		}
	}
}

.main-banner-content {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 20px;

	&::before {
		position: absolute;
		width: 100%;
		height: 300px;
		content: "";
		bottom: 0;
		z-index: 1;
		background: linear-gradient(180deg, rgba(236, 243, 250, 0.00) 24.31%, #F6F6F6 64.09%, #F6F6F6 100%);
	}
}

.main-banner-img-container {
	img {
		max-width: 100%;
		height: auto;
	}
}

.section-sp {
	position: relative;
	z-index: 2;

	.product-row {
		display: flex;
		justify-content: space-between;
		gap: 24px;
		margin-top: -160px;
		flex-wrap: wrap;
	}

	.product-col {
		background: #fff;
		border-radius: 20px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
		flex: 1 1 300px;
		min-width: 300px;
		display: flex;
		flex-direction: column;
		align-items: flex-start;
	}

	.product-col.green {
		border-radius: 30px;
		border: 1px solid #008F43;
		background: #FFF;
		box-shadow: 0px 3px 0px 12px rgba(248, 248, 248, 0.55) inset, 4px 5px 29px 0px rgba(0, 0, 0, 0.35);
	}

	.product-col.blue {
		border-radius: 30px;
		border: 1px solid #4a90e2;
		background: #FFF;
		box-shadow: 0px 3px 0px 12px rgba(248, 248, 248, 0.55) inset, 4px 5px 29px 0px rgba(0, 0, 0, 0.35);
	}

	.product-col.purple {
		border-radius: 30px;
		border: 1px solid #7b68ee;
		background: #FFF;
		box-shadow: 0px 3px 0px 12px rgba(248, 248, 248, 0.55) inset, 4px 5px 29px 0px rgba(0, 0, 0, 0.35);
	}

	.product-title {
		font-size: 35px;
		font-weight: bold;
		margin-bottom: 18px;
	}

	.product-title.green {
		color: #fff;
		background: linear-gradient(180deg, #72BF44 -50%, #007745 50%);
		width: 100%;
		padding: 20px 0;
		margin: 40px 0 20px 0;
		text-align: center;
	}

	.product-title.blue {
		color: #fff;
		background: linear-gradient(180deg, #58A2D9 -50%, #77E3FF 50%);
		width: 100%;
		padding: 20px 0;
		margin: 40px 0 20px 0;
		text-align: center;
	}

	.product-title.purple {
		color: #fff;
		background: linear-gradient(180deg, #D4D3FF -50%, #716FD0 50%);
		width: 100%;
		padding: 20px 0;
		margin: 40px 0 20px 0;
		text-align: center;
	}

	.product-list {
		list-style: none;
		padding: 0 30px;

		li {
			display: flex;
			align-items: flex-start;
			gap: 10px;
			margin-bottom: 20px;

			&:last-child {
				margin-bottom: 30px;
			}
		}

		.product-list-item {
			p {
				font-size: 30px;
				padding: 0;
				margin: 0;
				font-weight: 600;
			}
		}
	}

	.note-green {
		font-size: 12px;
		line-height: normal;

		a {
			color: #00a651;
			text-decoration: none;
		}
	}

	.checkmark {
		color: #00a651;
		font-size: 20px;
		margin-top: 2px;
	}

	.highlight-green {
		color: #00a651;
		font-weight: bold;
	}

	.highlight-blue {
		color: #4a90e2;
		font-weight: bold;
	}

	.highlight-purple {
		color: #7b68ee;
		font-weight: bold;
	}
}

.register-section {
	margin-top: 100px;
	text-align: center;

	.register-title {
		color: #58a2d9;
		font-size: 70px;
		font-weight: bold;
		margin-bottom: 36px;
	}

	.register-conditions {
		display: flex;
		justify-content: space-around;
		gap: 40px;
		flex-wrap: wrap;
		margin-bottom: 36px;
	}

	.register-col {
		display: flex;
		flex-direction: column;
		align-items: center;
		gap: 10px;
		position: relative;
	}

	.register-col-line {
		width: 1px;
		height: 450px;
		background: #008f43;
	}
}

.app-download-section {
	margin-top: 100px;
	text-align: center;
}

.app-download-content {
	display: flex;
	flex-direction: column;
	align-items: center;
}

.app-download-content-item {
	display: flex;
	align-items: center;
	gap: 50px;
	margin-bottom: 8px;
}

.app-download-content-item-content {
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 10px;
}

.app-download-content-item-content-title {
	font-size: 42px;
	font-weight: 600;
	color: #222;
	letter-spacing: 0.5px;
}

.app-download-content-item-content-app-store {
	display: flex;
	gap: 16px;
}

.vnd-black {
	color: #2e2e2e !important;
}

.vnd-blue {
	color: #3985c0 !important;
}

.section-support {
	display: block;
	width: 100%;
	margin-top: 20px;

	.support-content {
		display: block;
		width: 100%;
		padding: 30px;
		border-radius: 30px;
		box-shadow: 0 2px 12px rgba(0, 0, 0, 0.06);
		font-size: 24px;
		word-break: break-word;
		overflow-wrap: break-word;
		white-space: normal;
		box-sizing: border-box;
		overflow: auto;
	}
}

@media (max-width: 1199.98px) {
	.nav-btn {
		width: 100%;
		align-items: center;
		display: flex;
		justify-content: center;
	}

	.navbar {
		margin: 0 0 24px 0;
	}

	.navbar-logo {
		width: 70%;
		height: auto;
	}

	.navbar-title {
		font-size: 1.1rem;
	}

	.navbar-right {
		position: absolute;
		top: 60px;
		right: 0;
		background: #fff;
		flex-direction: column;
		align-items: flex-end;
		gap: 8px;
		padding: 50px 70px;
		border-radius: 0 0 15px 15px;
		display: none;
		box-shadow: 0 8px 24px 0 rgba(0, 0, 0, 0.08);
		z-index: 200;
	}

	.navbar-right.active {
		display: flex;
		align-items: center;
	}

	.navbar-toggle {
		display: flex;
	}

	.loan-estimate-section {
		.main-content {
			flex-direction: column;
			gap: 0;
			padding-left: 16px;
			padding-right: 16px;
		}

		.right {
			min-width: unset;
			margin-top: 24px;
		}

		.header {
			padding-left: 16px;
			padding-right: 16px;
		}

		.note {
			padding-left: 16px;
			padding-right: 16px;
		}
	}

	.simple-disbursement-section {
		.step {
			border-radius: 50px !important;
		}

		padding: 0 15px;
	}

	.faq-section {
		.faq-container {
			flex-direction: column;
			gap: 20px;
		}

		.faq-image {
			width: 100%;
		}

		.faq-content {
			padding-left: 0;
		}
	}

	.footer-content {
		flex-direction: column;
		align-items: flex-end;
		padding: 40px 20px;
		gap: 8px;
	}

	.footer-col {
		min-width: 0;
		width: 100%;
		margin-bottom: 10px;
	}

	.footer-social-icons {
		gap: 10px;
	}

	.footer-social-icon {
		width: 32px;
		height: 32px;

		img {
			width: 18px;
			height: 18px;
		}
	}

	.footer-contact-icon {
		width: 20px;
		height: 20px;
		font-size: 1rem;
	}

	.header {
		margin-top: 0 !important;
		text-align: center;
	}

	.main-banner-content {
		display: block;
	}

	.main-banner-img-container {
		img {
			max-width: 100%;
			height: auto;
		}
	}

	.header-container-sp {
		.header {
			h1 {
				font-size: 40px;
			}

			h2 {
				font-size: 35px;
				margin-top: 0;
			}
		}
	}

	.section-sp {
		.product-row {
			flex-direction: column;
			gap: 18px;
		}

		.product-col {
			margin: 0 20px;
		}
	}

	.register-section {
		.register-col-line {
			width: 90%;
			height: 1px;
			background: #008f43;
			margin: 50px auto;
		}

		.register-conditions {
			display: block !important;
		}

		.register-title {
			font-size: 60px;
			padding: 0 20px;
		}
	}

	.app-download-section {
		padding: 0 20px;
	}

	.app-download-content-item {
		display: block;
	}

	.app-download-content-item-content-app-store {
		img {
			max-width: 100%;
			height: auto !important;
		}
	}
}

@media (max-width: 400px) {
	.navbar {
		padding: 8px 2px 8px 2px;
		margin: 0 0 16px 0;
	}

	.navbar-title {
		display: none;
	}
}

@media screen and (max-width: 576px) {
	.main-banner-content-home {
		padding: 0 15px;
	}

	.section-support {
		.support-content {
			padding: 10px;
			font-size: 16px;
		}
	}
}

@media (max-width: 768px) {
	.loan-estimate-section {
		.header-loan {
			flex-direction: column-reverse;
			margin-left: 0;
		}

		.subtitle {
			padding: 0 20px;
			text-align: center;
		}
	}

	.features-section {
		.features {
			flex-direction: column;
		}
	}

	.simple-disbursement-section {
		.title-general {
			width: 90%;
			margin-top: -70px;
			font-size: 40px;
		}

		.steps-grid {
			grid-template-columns: 1fr;
			row-gap: 60px;
			margin-bottom: 50px;
		}

		.title {
			&::before {
				width: 10%;
			}

			&::after {
				width: 10%;
			}
		}

		.steps-container {
			padding: 20px 12px 0 30px;
			margin: 0 20px;
		}

		.step-content {
			font-size: 22px;
		}
	}

	.faq-section {
		.faq-container {
			flex-direction: column;
		}

		.faq-image {
			flex: 0 0 100%;
			padding-left: 0;
			margin-bottom: 20px;
		}

		.faq-content {
			flex: 0 0 100%;
			padding-left: 0;
		}

		.nav-buttons {
			flex-direction: column;
			gap: 10px;
			padding: 0 20px;
		}

		.nav-button {
			width: 100%;
		}
	}
}

@media (max-width: 576px) {
	.loan-estimate-section {
		.label {
			font-size: 20px;
		}

		.input-value {
			font-size: 25px;
		}

		.loan-term {
			font-size: 25px;
		}

		.monthly-value {
			font-size: 40px;
		}

		.note {
			padding: 0;
		}
	}

	.features-section {
		padding: 0 15px;
	}

	.app-promo-section {
		.app-promo-left-content {
			flex-direction: column;
		}

		.app-girl-img {
			margin-bottom: -20px;
		}

		.slogan-main {
			font-size: 40px;
		}

		.app-desc {
			font-size: 25px;
		}
	}

	.faq-section {
		.header {
			h1 {
				font-size: 40px;
			}

			padding: 0 20px;
		}
	}

	.header-container-sp {
		.header {
			h1 {
				font-size: 30px;
			}

			h2 {
				font-size: 25px;
				margin-top: 0;
			}
			.download-app {
				margin-top: 10px;
				margin-bottom: 20px;
				font-size: 20px;
				.download-app-icon {
					gap: 10px;
					a{
						img{
							width: 50px;
						}
					}
				}
			}
		}
	}
}

@media (max-width: 900px) {
	.app-promo-section {
		.app-promo-content {
			flex-direction: column;
			align-items: center;
			padding: 24px 0 16px 0;
		}

		.app-promo-left {
			align-items: center;
			padding-left: 0;
			text-align: center;
		}

		.app-divider {
			width: 60%;
			margin: 10px auto 10px auto;
		}

		.app-promo-right {
			justify-content: center;
			margin-top: 18px;
		}

		.app-girl-img {
			max-width: 100%;
		}
	}
}

@media screen and (max-width: 1199.98px) {
	.section-support {
		.support-content {
			padding: 20px;
		}
	}
}