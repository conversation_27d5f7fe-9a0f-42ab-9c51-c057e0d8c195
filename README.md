# My Angular App

Dự án này là một ứng dụng Angular mẫu.

## Cài đặt

```bash
npm install
```

## Chạy ứng dụng

```bash
ng serve
```

Truy cập: http://localhost:4200

## Build production

```bash
ng build --prod
```

## Th<PERSON> mục ch<PERSON>h
- `src/` - Mã nguồn ứng dụng
- `src/app/` - Các component, module chính
- `src/assets/` - Ảnh, file tĩnh

## Thông tin khác
- Đảm bảo đã cài đặt Node.js và Angular CLI trước khi chạy dự án.

## Development server

To start a local development server, run:

```bash
ng serve
```

Once the server is running, open your browser and navigate to `http://localhost:4200/`. The application will automatically reload whenever you modify any of the source files.

## Code scaffolding

Angular CLI includes powerful code scaffolding tools. To generate a new component, run:

```bash
ng generate component component-name
```

For a complete list of available schematics (such as `components`, `directives`, or `pipes`), run:

```bash
ng generate --help
```

## Running unit tests

To execute unit tests with the [Karma](https://karma-runner.github.io) test runner, use the following command:

```bash
ng test
```

## Running end-to-end tests

For end-to-end (e2e) testing, run:

```bash
ng e2e
```

Angular CLI does not come with an end-to-end testing framework by default. You can choose one that suits your needs.

## Additional Resources

For more information on using the Angular CLI, including detailed command references, visit the [Angular CLI Overview and Command Reference](https://angular.dev/tools/cli) page.
# cub-bank
