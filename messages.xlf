<?xml version="1.0" encoding="UTF-8" ?>
<xliff version="1.2" xmlns="urn:oasis:names:tc:xliff:document:1.2">
  <file source-language="en" datatype="plaintext" original="ng2.template">
    <body>
      <trans-unit id="footer.copyright" datatype="html">
        <source>© 2024 Your Company. All rights reserved.</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/footer/footer.component.html</context>
          <context context-type="linenumber">2,4</context>
        </context-group>
      </trans-unit>
      <trans-unit id="header.home" datatype="html">
        <source>Home</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/header.component.html</context>
          <context context-type="linenumber">3,4</context>
        </context-group>
      </trans-unit>
      <trans-unit id="header.product" datatype="html">
        <source>Product</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/header.component.html</context>
          <context context-type="linenumber">4,5</context>
        </context-group>
      </trans-unit>
      <trans-unit id="header.support" datatype="html">
        <source>Support</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/components/header/header.component.html</context>
          <context context-type="linenumber">5,7</context>
        </context-group>
      </trans-unit>
      <trans-unit id="home.welcome" datatype="html">
        <source>Welcome to our website</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/pages/home/<USER>/context>
          <context context-type="linenumber">2,3</context>
        </context-group>
      </trans-unit>
      <trans-unit id="home.description" datatype="html">
        <source>We provide the best services for our customers</source>
        <context-group purpose="location">
          <context context-type="sourcefile">src/app/pages/home/<USER>/context>
          <context context-type="linenumber">3,5</context>
        </context-group>
      </trans-unit>
    </body>
  </file>
</xliff>
