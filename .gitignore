# See https://docs.github.com/get-started/getting-started-with-git/ignoring-files for more about ignoring files.

# Node
node_modules/

# Build output
dist/
build/

# Environment files
.env
.env.*

# IDEs and editors
.idea/
.vscode/
*.sublime-workspace
*.sublime-project

# Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# OS
.DS_Store
Thumbs.db

# Angular specific
.angular/cache/
.angular/config.json

# Compiled output
tmp/
temp/

# Coverage
coverage/

# Misc
*.tgz

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache
